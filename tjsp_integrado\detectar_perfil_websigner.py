#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 DETECTOR DE PERFIL CHROME COM WEBSIGNER
Detecta perfis Chrome existentes que possuem a extensão WebSigner instalada
"""

import os
import json
import getpass
from pathlib import Path
import glob

def detectar_perfis_chrome():
    """Detecta todos os perfis Chrome disponíveis no sistema"""
    print("🔍 DETECTANDO PERFIS CHROME EXISTENTES")
    print("=" * 60)
    
    usuario = getpass.getuser()
    print(f"👤 Usuário: {usuario}")
    
    # Caminhos comuns para perfis Chrome no Windows
    caminhos_chrome = [
        f"C:/Users/<USER>/AppData/Local/Google/Chrome/User Data",
        f"C:/Users/<USER>/AppData/Local/Chromium/User Data",
        f"C:/Users/<USER>/ClineAutomationProfile_TJSP*",
        f"C:/Users/<USER>/AppData/Roaming/Code/User Data",
    ]
    
    perfis_encontrados = []
    
    for caminho_base in caminhos_chrome:
        print(f"\n🔍 Verificando: {caminho_base}")
        
        if "*" in caminho_base:
            # Usar glob para padrões com wildcard
            for caminho in glob.glob(caminho_base):
                if os.path.exists(caminho):
                    perfis = verificar_perfis_no_diretorio(caminho)
                    perfis_encontrados.extend(perfis)
        else:
            if os.path.exists(caminho_base):
                perfis = verificar_perfis_no_diretorio(caminho_base)
                perfis_encontrados.extend(perfis)
    
    return perfis_encontrados

def verificar_perfis_no_diretorio(caminho_base):
    """Verifica perfis em um diretório específico"""
    perfis = []
    
    try:
        # Verificar perfil Default
        perfil_default = os.path.join(caminho_base, "Default")
        if os.path.exists(perfil_default):
            info = analisar_perfil(perfil_default, "Default", caminho_base)
            if info:
                perfis.append(info)
        
        # Verificar outros perfis (Profile 1, Profile 2, etc.)
        for item in os.listdir(caminho_base):
            caminho_item = os.path.join(caminho_base, item)
            if os.path.isdir(caminho_item) and item.startswith("Profile"):
                info = analisar_perfil(caminho_item, item, caminho_base)
                if info:
                    perfis.append(info)
                    
    except Exception as e:
        print(f"⚠️ Erro ao verificar {caminho_base}: {e}")
    
    return perfis

def analisar_perfil(caminho_perfil, nome_perfil, caminho_base):
    """Analisa um perfil específico para verificar extensões"""
    try:
        # Verificar se existe diretório de extensões
        extensoes_dir = os.path.join(caminho_perfil, "Extensions")
        if not os.path.exists(extensoes_dir):
            return None
        
        # Procurar por WebSigner
        websigner_encontrado = False
        extensoes_instaladas = []
        
        for extensao_id in os.listdir(extensoes_dir):
            extensao_path = os.path.join(extensoes_dir, extensao_id)
            if os.path.isdir(extensao_path):
                extensoes_instaladas.append(extensao_id)
                
                # IDs conhecidos do WebSigner
                websigner_ids = [
                    "hhojmcideegphlbbfaidhblkjbpfkhoj",  # WebSigner comum
                    "bfnaelmomeimhlpmgjnjophhpkkoljpa",  # Lacuna WebSigner
                    "jbhkjlklkbkdmklkjlklkjlklkjlklkjl",  # Outro ID possível
                ]
                
                if extensao_id in websigner_ids:
                    websigner_encontrado = True
                
                # Verificar manifest para identificar WebSigner
                try:
                    versoes = os.listdir(extensao_path)
                    if versoes:
                        manifest_path = os.path.join(extensao_path, versoes[0], "manifest.json")
                        if os.path.exists(manifest_path):
                            with open(manifest_path, 'r', encoding='utf-8') as f:
                                manifest = json.load(f)
                                nome_ext = manifest.get('name', '').lower()
                                if 'websigner' in nome_ext or 'lacuna' in nome_ext:
                                    websigner_encontrado = True
                except:
                    pass
        
        # Verificar preferências do perfil
        prefs_path = os.path.join(caminho_perfil, "Preferences")
        certificados_configurados = False
        
        if os.path.exists(prefs_path):
            try:
                with open(prefs_path, 'r', encoding='utf-8') as f:
                    prefs = json.load(f)
                    # Verificar se há configurações de certificados
                    if 'profile' in prefs and 'content_settings' in prefs:
                        certificados_configurados = True
            except:
                pass
        
        info = {
            'nome': nome_perfil,
            'caminho_completo': caminho_base,
            'caminho_perfil': caminho_perfil,
            'websigner_instalado': websigner_encontrado,
            'certificados_configurados': certificados_configurados,
            'total_extensoes': len(extensoes_instaladas),
            'extensoes_ids': extensoes_instaladas[:5]  # Primeiras 5 para não poluir
        }
        
        return info
        
    except Exception as e:
        print(f"⚠️ Erro ao analisar perfil {nome_perfil}: {e}")
        return None

def exibir_resultados(perfis):
    """Exibe os resultados da detecção"""
    print("\n" + "=" * 60)
    print("📊 RESULTADOS DA DETECÇÃO")
    print("=" * 60)
    
    if not perfis:
        print("❌ Nenhum perfil Chrome encontrado")
        return None
    
    print(f"✅ {len(perfis)} perfil(s) encontrado(s)")
    
    melhor_perfil = None
    melhor_score = 0
    
    for i, perfil in enumerate(perfis, 1):
        print(f"\n🔹 PERFIL {i}: {perfil['nome']}")
        print(f"   📁 Caminho: {perfil['caminho_perfil']}")
        print(f"   🔌 WebSigner: {'✅ SIM' if perfil['websigner_instalado'] else '❌ NÃO'}")
        print(f"   🔐 Certificados: {'✅ SIM' if perfil['certificados_configurados'] else '❌ NÃO'}")
        print(f"   📦 Extensões: {perfil['total_extensoes']}")
        
        # Calcular score do perfil
        score = 0
        if perfil['websigner_instalado']:
            score += 10
        if perfil['certificados_configurados']:
            score += 5
        score += perfil['total_extensoes']
        
        if score > melhor_score:
            melhor_score = score
            melhor_perfil = perfil
    
    if melhor_perfil:
        print(f"\n🏆 MELHOR PERFIL DETECTADO:")
        print(f"   📛 Nome: {melhor_perfil['nome']}")
        print(f"   📁 Caminho: {melhor_perfil['caminho_completo']}")
        print(f"   🔌 WebSigner: {'✅' if melhor_perfil['websigner_instalado'] else '❌'}")
        print(f"   🔐 Certificados: {'✅' if melhor_perfil['certificados_configurados'] else '❌'}")
        print(f"   📊 Score: {melhor_score}")
    
    return melhor_perfil

def main():
    """Função principal"""
    print("🚀 INICIANDO DETECÇÃO DE PERFIL CHROME COM WEBSIGNER")
    print("=" * 60)
    
    perfis = detectar_perfis_chrome()
    melhor_perfil = exibir_resultados(perfis)
    
    if melhor_perfil:
        print(f"\n✅ Perfil recomendado encontrado!")
        print(f"📋 Para usar no código, configure:")
        print(f"PERFIL_CHROME_PATH = r\"{melhor_perfil['caminho_completo']}\"")
        
        # Salvar resultado em arquivo
        resultado = {
            'melhor_perfil': melhor_perfil,
            'todos_perfis': perfis,
            'recomendacao': melhor_perfil['caminho_completo']
        }
        
        with open('perfil_websigner_detectado.json', 'w', encoding='utf-8') as f:
            json.dump(resultado, f, indent=2, ensure_ascii=False)
        
        print(f"💾 Resultado salvo em: perfil_websigner_detectado.json")
        return melhor_perfil['caminho_completo']
    else:
        print(f"\n❌ Nenhum perfil adequado encontrado")
        print(f"💡 Recomendação: Configure um perfil Chrome com WebSigner manualmente")
        return None

if __name__ == "__main__":
    main()
