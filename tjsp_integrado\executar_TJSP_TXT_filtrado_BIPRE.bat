@echo off
title Processador TJSP Completo - Versão TXT Filtrado
color 0B

echo.
echo ================================================================================
echo                    PROCESSADOR TJSP COMPLETO - VERSÃO TXT FILTRADO
echo                                   (Usuario: Bipre)
echo ================================================================================
echo.
echo Este script processa numeros de autos de um arquivo TXT ja filtrado e limpo,
echo realizando validacao no portal TJSP e download automatico de oficios.
echo.

REM Verificar se Python está instalado
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ ERRO: Python nao encontrado!
    echo    Por favor, instale Python 3.7 ou superior
    echo    Download: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

REM Verificar se o arquivo TXT existe
if not exist "autosfiltrados.txt" (
    echo.
    echo ❌ AVISO: Arquivo 'autosfiltrados.txt' nao encontrado!
    echo.
    echo    Para usar este script, voce precisa:
    echo    1. Executar o verificador de duplicados 2010+
    echo    2. Renomear o arquivo gerado para 'autosfiltrados.txt'
    echo    3. Colocar o arquivo nesta pasta
    echo.
    echo    Exemplo de arquivo gerado pelo verificador:
    echo    'Numeros_Limpos_2010Plus_20241227_143022.txt'
    echo.
    echo    Deseja continuar mesmo assim? O script permitira
    echo    digitar o caminho de um arquivo alternativo.
    echo.
    set /p continuar="Digite S para continuar ou qualquer tecla para sair: "
    if /i not "%continuar%"=="s" (
        echo.
        echo Encerrando...
        timeout /t 2 >nul
        exit /b 1
    )
)

REM Verificar ChromeDriver
echo 🔍 Verificando ChromeDriver...
if exist "drivers\chromedriver.exe" (
    echo ✅ ChromeDriver encontrado na pasta drivers/
) else (
    if exist "C:\Users\<USER>\.wdm\drivers\chromedriver\" (
        echo ✅ ChromeDriver encontrado no WebDriver Manager
    ) else (
        if exist "C:\Users\<USER>\.cache\selenium\chromedriver\" (
            echo ✅ ChromeDriver encontrado no Selenium Manager
        ) else (
            echo ⚠️ ChromeDriver nao encontrado nos locais padroes
            echo    O script tentara usar gerenciamento automatico do Selenium
        )
    )
)

REM Verificar dependências Python
echo 🔍 Verificando dependencias Python...
python -c "import selenium, pandas, tqdm" >nul 2>&1
if errorlevel 1 (
    echo.
    echo ⚠️ Algumas dependencias Python estao faltando.
    echo    Instalando automaticamente...
    echo.
    pip install selenium pandas openpyxl tqdm PyMuPDF
    
    if errorlevel 1 (
        echo.
        echo ❌ Falha ao instalar dependencias.
        echo    Execute manualmente: pip install selenium pandas openpyxl tqdm PyMuPDF
        echo.
        pause
        exit /b 1
    )
)

echo ✅ Dependencias verificadas com sucesso!
echo.

REM Mostrar informações do arquivo TXT
if exist "autosfiltrados.txt" (
    echo 📋 Informacoes do arquivo TXT:
    for /f %%i in ('find /c /v "" ^< autosfiltrados.txt') do set linhas=%%i
    echo    Arquivo: autosfiltrados.txt
    echo    Total de linhas: !linhas!
    echo.
) else (
    echo ℹ️ Arquivo 'autosfiltrados.txt' nao encontrado.
    echo    O script permitira especificar um arquivo alternativo.
    echo.
)

REM Avisos importantes
echo 🚨 IMPORTANTE - ANTES DE CONTINUAR:
echo.
echo    1. Certifique-se de que o Chrome esta fechado
echo    2. Tenha suas credenciais do TJSP em maos
echo    3. Este processo pode demorar varias horas
echo    4. Mantenha o computador ligado durante todo o processamento
echo.

echo 🔧 CONFIGURACOES ATUAIS:
echo    • Usuario: Bipre
echo    • Perfil Chrome: C:/Users/<USER>/ClineAutomationProfile_TJSP
echo    • Pasta Downloads: downloads_completos/
echo    • Pasta Logs: logs_completos/
echo    • ChromeDriver: Busca automatica em drivers/, .wdm, .cache
echo.

set /p confirmar="Digite S para iniciar o processamento ou qualquer tecla para sair: "
if /i not "%confirmar%"=="s" (
    echo.
    echo Processamento cancelado pelo usuario.
    timeout /t 2 >nul
    exit /b 0
)

echo.
echo 🚀 Iniciando Processador TJSP Completo - Versão TXT Filtrado...
echo.
echo ================================================================================
echo                                PROCESSAMENTO INICIADO
echo ================================================================================
echo.

REM Executar o script principal
python TJSP_completo_TXT_filtrado.py

REM Verificar se houve erro
if errorlevel 1 (
    echo.
    echo ================================================================================
    echo                                   ERRO DETECTADO
    echo ================================================================================
    echo.
    echo ❌ O processamento foi interrompido devido a um erro.
    echo    Verifique os logs para mais detalhes.
    echo.
) else (
    echo.
    echo ================================================================================
    echo                                PROCESSAMENTO CONCLUÍDO
    echo ================================================================================
    echo.
    echo ✅ Processamento finalizado com sucesso!
    echo.
    echo 📁 Verifique os arquivos gerados:
    echo    • Relatorio Excel: resultados_completos_TJSP_TXT_*.xlsx
    echo    • Downloads PDF: downloads_completos/
    echo    • Log detalhado: logs_completos/
    echo.
)

echo Pressione qualquer tecla para sair...
pause >nul
