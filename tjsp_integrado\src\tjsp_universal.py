#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TJSP Universal - Sistema Universal de Automação TJSP
Sistema refatorado que funciona para qualquer usuário automaticamente
"""

import os
import sys
import time
import glob
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional

# Selenium imports
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

# Sistema universal
from universal_system_manager import UniversalSystemManager

class TJSPUniversal:
    """
    Sistema Universal TJSP - Funciona automaticamente para qualquer usuário
    """
    
    def __init__(self):
        # Inicializar sistema universal
        self.system_manager = UniversalSystemManager()
        self.logger = self.system_manager.logger
        
        # Configurações do TJSP
        self.tjsp_config = self.system_manager.user_config['tjsp_settings']
        self.webdriver_config = self.system_manager.user_config['webdriver_settings']
        
        # URLs
        self.URL_TJSP_CONSULTA = self.tjsp_config['main_url']
        self.URL_TJSP_LOGIN = self.tjsp_config['login_url']
        
        # Diretórios
        self.download_dir = self.system_manager.get_download_directory()
        self.chrome_profile_path = self.system_manager.get_chrome_profile_path()
        
        # Driver
        self.driver = None
        
        self.logger.info("🚀 TJSP Universal inicializado")
        self.logger.info(f"👤 Usuário: {self.system_manager.current_user}")
        self.logger.info(f"📁 Downloads: {self.download_dir}")
        self.logger.info(f"🌐 Perfil Chrome: {self.chrome_profile_path}")
    
    def configure_chrome_options(self) -> Options:
        """Configura opções do Chrome de forma universal"""
        chrome_options = Options()
        
        # Configurações básicas
        chrome_options.add_argument("--start-maximized")
        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_argument("--disable-popup-blocking")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option("useAutomationExtension", False)
        
        # Perfil do usuário (específico para cada usuário) - Comentado temporariamente para teste
        # chrome_options.add_argument(f'--user-data-dir={self.chrome_profile_path}')
        
        # User agent
        if self.webdriver_config.get('user_agent'):
            chrome_options.add_argument(f"--user-agent={self.webdriver_config['user_agent']}")
        
        # Headless mode (se configurado)
        if self.webdriver_config.get('headless', False):
            chrome_options.add_argument("--headless")
        
        # Configurações de download
        prefs = {
            "download.default_directory": self.download_dir,
            "download.prompt_for_download": False,
            "download.directory_upgrade": True,
            "safebrowsing.enabled": True,
            "plugins.always_open_pdf_externally": True
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        self.logger.info("⚙️ Opções do Chrome configuradas")
        return chrome_options
    
    def initialize_webdriver(self) -> webdriver.Chrome:
        """Inicializa WebDriver usando sistema universal"""
        try:
            # Obter ChromeDriver automaticamente
            chromedriver_path = self.system_manager.get_chrome_driver_path()
            
            # Configurar opções
            chrome_options = self.configure_chrome_options()
            
            # Criar serviço
            service = Service(executable_path=chromedriver_path)
            
            # Inicializar driver
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            
            # Configurações adicionais
            self.driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            self.logger.info("✅ WebDriver inicializado com sucesso")
            self.logger.info(f"🔧 ChromeDriver: {chromedriver_path}")
            
            return self.driver
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao inicializar WebDriver: {e}")
            raise
    
    def navigate_to_tjsp(self) -> bool:
        """Navega para página principal do TJSP"""
        try:
            self.logger.info("🌐 Navegando para TJSP...")
            self.driver.get(self.URL_TJSP_CONSULTA)
            
            # Aguardar carregamento
            WebDriverWait(self.driver, self.tjsp_config['timeout']).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            self.logger.info("✅ Página TJSP carregada")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao navegar para TJSP: {e}")
            return False
    
    def find_search_field(self) -> Optional[object]:
        """Localiza campo de pesquisa usando seletor universal"""
        try:
            # Usar seletor descoberto pela análise Playwright
            search_selector = self.tjsp_config['search_field_selector']
            
            self.logger.info(f"🔍 Procurando campo de pesquisa: {search_selector}")
            
            search_field = WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, search_selector))
            )
            
            self.logger.info("✅ Campo de pesquisa encontrado")
            return search_field
            
        except TimeoutException:
            self.logger.warning("⚠️ Campo de pesquisa não encontrado com seletor padrão")
            
            # Tentar seletores alternativos
            alternative_selectors = [
                '#numeroDigitoAnoUnificado',
                'input[name="numeroDigitoAnoUnificado"]',
                'input[id*="numero"]',
                'input[name*="processo"]'
            ]
            
            for selector in alternative_selectors:
                try:
                    search_field = self.driver.find_element(By.CSS_SELECTOR, selector)
                    self.logger.info(f"✅ Campo encontrado com seletor alternativo: {selector}")
                    return search_field
                except NoSuchElementException:
                    continue
            
            self.logger.error("❌ Campo de pesquisa não encontrado")
            return None
    
    def search_process(self, process_number: str) -> bool:
        """Pesquisa processo no TJSP"""
        try:
            self.logger.info(f"🔍 Pesquisando processo: {process_number}")
            
            # Localizar campo de pesquisa
            search_field = self.find_search_field()
            if not search_field:
                return False
            
            # Limpar e preencher campo
            search_field.clear()
            search_field.send_keys(process_number)
            
            # Aguardar um pouco
            time.sleep(1)
            
            # Procurar botão de pesquisa
            search_button_selectors = [
                'input[type="submit"]',
                'button[type="submit"]',
                '.btn-primary',
                '#botaoConsultarProcessos'
            ]
            
            search_button = None
            for selector in search_button_selectors:
                try:
                    search_button = self.driver.find_element(By.CSS_SELECTOR, selector)
                    break
                except NoSuchElementException:
                    continue
            
            if search_button:
                search_button.click()
                self.logger.info("✅ Pesquisa executada")
                
                # Aguardar resultado
                time.sleep(3)
                return True
            else:
                self.logger.error("❌ Botão de pesquisa não encontrado")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Erro ao pesquisar processo: {e}")
            return False
    
    def process_txt_file(self, txt_file_path: str) -> Dict:
        """Processa arquivo TXT com números de processos"""
        try:
            self.logger.info(f"📄 Processando arquivo: {txt_file_path}")
            
            if not os.path.exists(txt_file_path):
                self.logger.error(f"❌ Arquivo não encontrado: {txt_file_path}")
                return {"success": False, "error": "Arquivo não encontrado"}
            
            # Ler números do arquivo
            with open(txt_file_path, 'r', encoding='utf-8') as f:
                process_numbers = [line.strip() for line in f if line.strip()]
            
            self.logger.info(f"📊 {len(process_numbers)} processos encontrados no arquivo")
            
            results = {
                "success": True,
                "total_processes": len(process_numbers),
                "processed": 0,
                "successful": 0,
                "failed": 0,
                "results": []
            }
            
            # Processar cada número
            for i, process_number in enumerate(process_numbers, 1):
                self.logger.info(f"🔄 Processando {i}/{len(process_numbers)}: {process_number}")
                
                try:
                    # Navegar para página principal
                    if not self.navigate_to_tjsp():
                        continue
                    
                    # Pesquisar processo
                    if self.search_process(process_number):
                        results["successful"] += 1
                        results["results"].append({
                            "process_number": process_number,
                            "status": "success",
                            "timestamp": datetime.now().isoformat()
                        })
                    else:
                        results["failed"] += 1
                        results["results"].append({
                            "process_number": process_number,
                            "status": "failed",
                            "error": "Erro na pesquisa",
                            "timestamp": datetime.now().isoformat()
                        })
                    
                    results["processed"] += 1
                    
                    # Pausa entre processos
                    time.sleep(2)
                    
                except Exception as e:
                    self.logger.error(f"❌ Erro ao processar {process_number}: {e}")
                    results["failed"] += 1
                    results["results"].append({
                        "process_number": process_number,
                        "status": "error",
                        "error": str(e),
                        "timestamp": datetime.now().isoformat()
                    })
            
            # Salvar relatório
            report_file = os.path.join(self.download_dir, f"tjsp_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📊 Relatório salvo: {report_file}")
            self.logger.info(f"✅ Processamento concluído: {results['successful']}/{results['total_processes']} sucessos")
            
            return results
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao processar arquivo TXT: {e}")
            return {"success": False, "error": str(e)}
    
    def cleanup(self):
        """Limpa recursos"""
        if self.driver:
            try:
                self.driver.quit()
                self.logger.info("🧹 WebDriver finalizado")
            except Exception as e:
                self.logger.error(f"❌ Erro ao finalizar WebDriver: {e}")
    
    def __enter__(self):
        """Context manager entry"""
        self.initialize_webdriver()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Context manager exit"""
        self.cleanup()

def main():
    """Função principal para teste"""
    print("🎯 TJSP Universal - Teste do Sistema")
    
    try:
        with TJSPUniversal() as tjsp:
            # Testar navegação
            if tjsp.navigate_to_tjsp():
                print("✅ Navegação para TJSP bem-sucedida")
                
                # Testar pesquisa
                test_number = "1234567-89.2023.8.26.0001"
                if tjsp.search_process(test_number):
                    print("✅ Teste de pesquisa bem-sucedido")
                else:
                    print("⚠️ Teste de pesquisa falhou (esperado para número fictício)")
            else:
                print("❌ Falha na navegação para TJSP")
    
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
