{"name": "Extração - TJSP_Completo - Final 03/06", "nodes": [{"parameters": {"content": "# Adicionar/Atualizar a Extração de um Ofício Requisitório", "height": 80, "width": 933, "color": 5}, "id": "e99dd63f-23da-420a-972d-9195b42ed7a0", "name": "Sticky Note1", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 0]}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.file_id }}", "mode": "id"}, "options": {"googleFileConversion": {"conversion": {"docsToFormat": "text/plain"}}}}, "id": "c2f05189-8e0b-46d6-b8bc-321fd38cdacf", "name": "Download File", "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [820, 280], "executeOnce": true, "credentials": {"googleDriveOAuth2Api": {"id": "xZgfJ2mA5gyFAufR", "name": "Google Drive account"}}, "onError": "continueRegularOutput"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyX", "value": 9, "unit": "minutes"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ", "mode": "id"}, "event": "fileCreated", "options": {}}, "id": "e47ffc18-262a-41da-bb35-29a06177358a", "name": "File Created", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [80, 200], "credentials": {"googleDriveOAuth2Api": {"id": "xZgfJ2mA5gyFAufR", "name": "Google Drive account"}}}, {"parameters": {"pollTimes": {"item": [{"mode": "everyX", "value": 29, "unit": "minutes"}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ", "mode": "id"}, "event": "fileUpdated", "options": {}}, "id": "9dd13d34-a576-472a-9e4e-d87348097d25", "name": "File Updated", "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [80, 380], "credentials": {"googleDriveOAuth2Api": {"id": "xZgfJ2mA5gyFAufR", "name": "Google Drive account"}}}, {"parameters": {"assignments": {"assignments": [{"id": "10646eae-ae46-4327-a4dc-9987c2d76173", "name": "file_id", "value": "={{ $json.id }}", "type": "string"}, {"id": "f4536df5-d0b1-4392-bf17-b8137fb31a44", "name": "file_type", "value": "={{ $json.mimeType }}", "type": "string"}, {"id": "c774ed34-0d67-44b7-a537-83810f357b7c", "name": "originalFilename", "value": "={{ $json.originalFilename }}", "type": "string"}, {"id": "dff39c85-b4a2-45ba-a5ff-f4b311999efc", "name": "sha1Checksum", "value": "={{ $json.sha1Checksum }}", "type": "string"}, {"id": "bf1c6969-fcb6-4d3a-adb9-499e9b2bd081", "name": "ID_planilha", "value": "17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw", "type": "string"}, {"id": "06e88c68-d2f4-43fd-ac8b-03812f40b879", "name": "horario_upload", "value": "={{ $json.createdTime }}", "type": "string"}]}, "options": {}}, "id": "bcb018b2-c218-440c-827a-32e1ee0f58ba", "name": "Set File ID", "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [300, 280]}, {"parameters": {"operation": "pdf", "options": {}}, "id": "bf51d528-616c-4314-93a2-4cbbaf3273fd", "name": "Extract PDF Text", "type": "n8n-nodes-base.extractFromFile", "typeVersion": 1, "position": [1180, 280], "onError": "continueRegularOutput"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"leftValue": "={{ $json.file_type }}", "rightValue": "application/pdf", "operator": {"type": "string", "operation": "equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "2ae7faa7-a936-4621-a680-60c512163034", "leftValue": "={{ $json.file_type }}", "rightValue": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 1}, "conditions": [{"id": "fc193b06-363b-4699-a97d-e5a850138b0e", "leftValue": "={{ $json.file_type }}", "rightValue": "application/vnd.google-apps.document", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}}]}, "options": {"fallbackOutput": 2}}, "id": "0a00ab30-0f8c-410c-8f56-9d4bebfddafc", "name": "Switch", "type": "n8n-nodes-base.switch", "typeVersion": 3, "position": [980, 280], "onError": "continueRegularOutput"}, {"parameters": {"content": "", "height": 700, "width": 2900, "color": 3}, "type": "n8n-nodes-base.stickyNote", "position": [0, 0], "typeVersion": 1, "id": "990a82a3-3799-4e54-bd93-907c25ea619c", "name": "Sticky Note6"}, {"parameters": {"content": "## Gatilho de Monitoramento", "height": 460, "width": 213, "color": 5}, "id": "1a2318fa-bab7-4d1b-a6fd-df03f622d00e", "name": "Sticky Note10", "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [40, 100]}, {"parameters": {"options": {}}, "type": "n8n-nodes-base.splitInBatches", "typeVersion": 3, "position": [640, 280], "id": "0341de05-6006-4489-826a-655cf15f4d03", "name": "Loop Over Items"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [1180, 500], "id": "f8a3fad1-bbbf-412d-84e7-5f810ca39af8", "name": "No Operation, do nothing"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4o-mini", "mode": "list", "cachedResultName": "GPT-4O-MINI"}, "messages": {"values": [{"content": "=Você é um agente de inteligência artificial especialista em extração de dados de Ofício Requisitórios de Precatórios, especialista identificar e extrair dados a partir de textos.", "role": "system"}, {"content": "=Aqui está o texto do documento que você realiza a identificação e a extração perfeita dos dados de ofícios requisitórios para o pagamento de precatórios:  {{ $json.text }}"}, {"content": "=Extraia todas as informações do Ofício Requisitório de Precatório do TJSP em formato JSON estruturado. Siga as seguintes regras: \n(1) Formate todas as datas no padrão DD/MM/AAAA; \n(2) Valores monetários devem manter o formato brasileiro (R$ 0.000,00); \n(3) Se algum campo não for encontrado, deixe-o como 'n/c' ou o valor correspondente a 'não se aplica'; \n(4) Forneça apenas o JSON resultante, sem comentários adicionais.\"\n\n\"output_format\": {\n  \"basico\": {\n    \"nome\": \"\",\n    \"cpf\": \"\",          // ### CAMPO OBRIGATÓRIO ###\n    \"cpf_limpo\": \"\",    // só dígitos\n  },\n  \"dados_pessoais\": {\n    \"data_nascimento\": \"\",\n    \"idade\": \"\", //considerando o ano do DateTime: {{ $now }}\n    \"prioridade\": \"Nenhuma | Idoso | DoencaGrave | PCD\",\n  },\n  \"processo\": {\n    \"numero_execucao\": \"\", //Processo nº:\n    \"numero_principal\": \"\", //Processo Principal/Conhecimento:\n    \"tribunal\": \"\",\n    \"cidade\": \"\",\n    \"comarca\": \"\",\n    \"foro\": \"\",\n    \"vara\": \"\",\n    \"documento\": \"\", //SOMENTE o que vier depois de \"OFÍCIO REQUISITÓRIO Nº\" (ex.: \"142/2021\" ou \"*\")\n    \"natureza\": \"\", //Alimentar | Outros\n  },\n  \"valores\": {\n    \"total\": \"\",\n    \"principal\": \"\",\n    \"juros\": \"\",\n    \"pss\": \"\", // contribuição previdenciária (se houver = Sim, se n/c = Não)\n  },\n  \"datas\": {\n    \"data_base\": \"\",\n    \"data_expedicao\": \"\"\n  },\n  \"bancarios\": {\n    \"tipo_deposito\": \"\", // Conta, Comparecer, etc.\n    \"banco\": \"\",\n    \"agencia\": \"\",\n    \"conta\": \"\"\n  },\n  \"partes\": {\n    \"devedor\": \"\",\n    \"procurador\": \"\",\n    \"oab_procurador\": \"\",\n    \"advogado\": \"\",\n    \"oab_advogado\": \"\"\n  },\n}\n\n\nSe não localizar CPF, procure qualquer padrão XXX.XXX.XXX-XX no documento; se ainda não encontrar, devolva campo cpf como n/c.\nPara o campo processo.documento, devolva exclusivamente o conteúdo que aparece após a expressão “OFÍCIO REQUISITÓRIO Nº”, sem o rótulo nem espaços extras.", "role": "assistant"}]}, "jsonOutput": true, "options": {"temperature": 0}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [1320, 280], "id": "b499aa5d-ea40-403d-9f2f-0e6dcec6387f", "name": "OpenAI", "credentials": {"openAiApi": {"id": "hSAvAjq8FRV20iAU", "name": "API Bipre"}}, "onError": "continueRegularOutput"}, {"parameters": {"amount": 1}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [2700, 500], "id": "c48f54e7-1a04-4fb8-a8c6-347ac0d60036", "name": "Wait", "webhookId": "8eb88be4-dea6-4a47-abaa-e4ab00e04c96", "onError": "continueRegularOutput"}, {"parameters": {"jsCode": "return $items(\"Set File ID\").map(item => {\n  return {\n    json: {\n      file_id: item.json.file_id,\n      file_type: item.json.file_type,\n      file_name: item.json.originalFilename\n    }\n  };\n});"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [460, 280], "id": "a3dc3264-a834-419c-b2ee-15dd80e70a3e", "name": "Code"}, {"parameters": {}, "type": "n8n-nodes-base.noOp", "typeVersion": 1, "position": [820, 120], "id": "e8092e41-e060-4aac-a86e-693a3ca854dd", "name": "No Operation, do nothing1"}, {"parameters": {"url": "=https://api.mytrust.space/v1/cpf/{{ $json.message.content.basico.cpf_limpo }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "x-trust-key", "value": "sk_01jqxhjw6bx3nkxndccnq55zey01jqxhjw6bjb4xp5mqa5zcwhtq"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1920, 280], "id": "bc65169c-b9bd-41a9-9d8c-9e8a4ccff9a3", "name": "Enriquecimento Tel", "disabled": true, "onError": "continueRegularOutput"}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "=17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw", "mode": "id"}, "sheetName": {"__rl": true, "value": 1123162002, "mode": "list", "cachedResultName": "<PERSON><PERSON>", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw/edit#gid=1123162002"}, "columns": {"mappingMode": "defineBelow", "value": {"Nº PROCESSO (Completo)": "={{ $('OpenAI').item.json.message.content.processo.numero_execucao.replace(/\\/(\\d+)$/, ' ($1)') }}", "Nº PROCESSO (Sem Prec)": "={{ $('OpenAI').item.json.message.content.processo.numero_execucao.split('/')[0] }}", "CPF": "={{ $('OpenAI').item.json.message.content.basico.cpf }}", "CPF (Limpo)": "={{ $('OpenAI').item.json.message.content.basico.cpf_limpo }}", "Data de Nascimento": "={{ $('OpenAI').item.json.message.content.dados_pessoais.data_nascimento }}", "Nome": "={{ $('OpenAI').item.json.message.content.basico.nome }}", "Idade": "={{ $('OpenAI').item.json.message.content.dados_pessoais.idade }}", "Prioridade": "={{ $('OpenAI').item.json.message.content.dados_pessoais.prioridade }}", "Tribunal": "={{ $('OpenAI').item.json.message.content.processo.tribunal }}", "DEVEDOR": "={{ $('OpenAI').item.json.message.content.partes.devedor }}", "VARA": "={{ $('OpenAI').item.json.message.content.processo.vara }}", "CIDADE": "={{ $('OpenAI').item.json.message.content.processo.cidade }}", "DATA BASE": "={{ $('OpenAI').item.json.message.content.datas.data_base }}", "DATA EXPEDIÇÃO": "={{ $('OpenAI').item.json.message.content.datas.data_expedicao }}", "VALOR TOTAL": "={{ $('OpenAI').item.json.message.content.valores.total }}", "JUROS": "={{ $('OpenAI').item.json.message.content.valores.juros }}", "NÚMERO DO PRECATÓRIO": "={{ $('OpenAI').item.json.message.content.processo.documento }}", "Procurador": "={{ $('OpenAI').item.json.message.content.partes.procurador }}", "OAB Procurador": "={{ $('OpenAI').item.json.message.content.partes.oab_procurador }}", "Advogado": "={{ $('OpenAI').item.json.message.content.partes.advogado }}", "OAB Advogado": "={{ $('OpenAI').item.json.message.content.partes.oab_advogado }}", "PDF no DRIVE": "={{ $('Download File').item.json.file_name }}", "Validação": "=Integrado - Final", "Natureza": "={{ $('OpenAI').item.json.message.content.processo.natureza }}", "PSS": "={{ $('OpenAI').item.json.message.content.valores.pss }}", "Tipo Deposito": "={{ $('OpenAI').item.json.message.content.bancarios.tipo_deposito }}", "Banco": "={{ $('OpenAI').item.json.message.content.bancarios.banco }}", "Agência": "={{ $('OpenAI').item.json.message.content.bancarios.agencia }}", "Conta": "={{ $('OpenAI').item.json.message.content.bancarios.conta }}", "Telefone 1 - API": "=", "Telefone 2 - API": "=", "Telefone 3 - API": "=", "Telefone 4 - API": "=", "Telefone 5 - API": "=", "Valor Principal": "={{ $('OpenAI').item.json.message.content.valores.principal }}", "Data da extração": "={{ (new Date()).toLocaleDateString('pt-BR') }}", "Certidão": "={{ $('Descarte CNPJ').item.json.certidao_status }}"}, "matchingColumns": ["Nº PROCESSO (Completo)"], "schema": [{"id": "Nº PROCESSO (Completo)", "displayName": "Nº PROCESSO (Completo)", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Nº PROCESSO (Sem Prec)", "displayName": "Nº PROCESSO (Sem Prec)", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Nome", "displayName": "Nome", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "CPF", "displayName": "CPF", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "CPF (Limpo)", "displayName": "CPF (Limpo)", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "TELEFONE 1", "displayName": "TELEFONE 1", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "TELEFONE 2", "displayName": "TELEFONE 2", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Data de Nascimento", "displayName": "Data de Nascimento", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON>", "displayName": "<PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Prioridade", "displayName": "Prioridade", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Tribunal", "displayName": "Tribunal", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "DEVEDOR", "displayName": "DEVEDOR", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "VARA", "displayName": "VARA", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Natureza", "displayName": "Natureza", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "CIDADE", "displayName": "CIDADE", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "DATA BASE", "displayName": "DATA BASE", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "DATA EXPEDIÇÃO", "displayName": "DATA EXPEDIÇÃO", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "VALOR TOTAL", "displayName": "VALOR TOTAL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "JUROS", "displayName": "JUROS", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON>", "displayName": "<PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "NÚMERO DO PRECATÓRIO", "displayName": "NÚMERO DO PRECATÓRIO", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "PSS", "displayName": "PSS", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Banco", "displayName": "Banco", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Agência", "displayName": "Agência", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Conta", "displayName": "Conta", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Procurador", "displayName": "Procurador", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "OAB Procurador", "displayName": "OAB Procurador", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Advogado", "displayName": "Advogado", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "OAB Advogado", "displayName": "OAB Advogado", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "PDF no DRIVE", "displayName": "PDF no DRIVE", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Data da extração", "displayName": "Data da extração", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Nº Processo - Luan", "displayName": "Nº Processo - Luan", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Validação", "displayName": "Validação", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Telefone 1 - API", "displayName": "Telefone 1 - API", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Telefone 2 - API", "displayName": "Telefone 2 - API", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Telefone 3 - API", "displayName": "Telefone 3 - API", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Telefone 4 - API", "displayName": "Telefone 4 - API", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "Telefone 5 - API", "displayName": "Telefone 5 - API", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [2080, 280], "id": "49aae81c-3fd5-4ab9-8ea6-e448afb09300", "name": "Base de Dados", "credentials": {"googleSheetsOAuth2Api": {"id": "6VuUVtVrNszA1WU1", "name": "Google Sheets account"}}, "onError": "continueRegularOutput"}, {"parameters": {"rules": {"values": [{"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"leftValue": "={{ $('Valida CPF + Valor').item.json.categoria_valor }}", "rightValue": "BAIXO", "operator": {"type": "string", "operation": "equals"}, "id": "c10be9f3-6f74-46a4-962e-1ea56c464437"}], "combinator": "and"}, "renameOutput": true, "outputKey": "BAIXO"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "35491f68-6fd2-4beb-8b47-9fe066e49666", "leftValue": "={{ $('Valida CPF + Valor').item.json.categoria_valor }}", "rightValue": "MÉDIO", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "MEDIO"}, {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "726ef46f-67e4-4808-b4be-f6a9cd857d81", "leftValue": "={{ $('Valida CPF + Valor').item.json.categoria_valor }}", "rightValue": "ALTO", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "renameOutput": true, "outputKey": "ALTO"}]}, "options": {}}, "type": "n8n-nodes-base.switch", "typeVersion": 3.2, "position": [2240, 280], "id": "11f22aa9-32ca-4167-b77c-b5b5d4a94d3f", "name": "Filtro de Valores"}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw", "mode": "list", "cachedResultName": "NOMES PRECATÓRIO", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": 95921779, "mode": "list", "cachedResultName": "NOMES PRECATÓRIOS ESTADUAIS -25K", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw/edit#gid=95921779"}, "columns": {"mappingMode": "defineBelow", "value": {"Nº PROCESSO": "={{ $json['Nº PROCESSO (Completo)'] }}", "NOME": "={{ $json.Nome }}", "CPF": "={{ $json.CPF }}", "VALOR TOTAL": "={{ $json['VALOR TOTAL'] }}", "JUROS": "={{ $json.JUROS }}", "VALOR PRINCIPAL": "={{ $json['<PERSON><PERSON> Principal'] }}", "Ofício": "={{ $('Download File').item.json.file_name }}"}, "matchingColumns": ["Nº PROCESSO"], "schema": [{"id": "NOME", "displayName": "NOME", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Nº PROCESSO", "displayName": "Nº PROCESSO", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "TELEFONE ", "displayName": "TELEFONE ", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "CPF", "displayName": "CPF", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "OBS", "displayName": "OBS", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "ÚLTIMA INTERAÇÃO", "displayName": "ÚLTIMA INTERAÇÃO", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "DATA", "displayName": "DATA", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "TELEFONE 1", "displayName": "TELEFONE 1", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "TELEFONE 2", "displayName": "TELEFONE 2", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "VALOR TOTAL", "displayName": "VALOR TOTAL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "JUROS", "displayName": "JUROS", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "VALOR PRINCIPAL", "displayName": "VALOR PRINCIPAL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Status Certidão", "displayName": "Status Certidão", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [2460, 40], "id": "13f7e394-2446-469b-b1f9-19a6d979ae80", "name": "Aba -25k", "credentials": {"googleSheetsOAuth2Api": {"id": "6VuUVtVrNszA1WU1", "name": "Google Sheets account"}}}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw", "mode": "list", "cachedResultName": "NOMES PRECATÓRIO", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "NOMES PRECATÓRIOS ESTADUAIS +25K", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw/edit#gid=**********"}, "columns": {"mappingMode": "defineBelow", "value": {"Nº PROCESSO": "={{ $json['Nº PROCESSO (Completo)'] }}", " NOME": "={{ $json.Nome }}", "CPF": "={{ $json.CPF }}", "VALOR TOTAL": "={{ $json['VALOR TOTAL'] }}", "JUROS": "={{ $json.JUROS }}", "VALOR PRINCIPAL": "={{ $json['<PERSON><PERSON> Principal'] }}", "Ofício": "={{ $('Download File').item.json.file_name }}"}, "matchingColumns": ["Nº PROCESSO"], "schema": [{"id": " NOME", "displayName": " NOME", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Nº PROCESSO", "displayName": "Nº PROCESSO", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "TELEFONE ", "displayName": "TELEFONE ", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "CPF", "displayName": "CPF", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "OBS", "displayName": "OBS", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "ÚLTIMA INTERAÇÃO", "displayName": "ÚLTIMA INTERAÇÃO", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "DATA", "displayName": "DATA", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "TELEFONE 1", "displayName": "TELEFONE 1", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "TELEFONE 2", "displayName": "TELEFONE 2", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "VALOR TOTAL", "displayName": "VALOR TOTAL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "JUROS", "displayName": "JUROS", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "VALOR PRINCIPAL", "displayName": "VALOR PRINCIPAL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Status Certidão", "displayName": "Status Certidão", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [2460, 200], "id": "114762f0-87c6-4b8f-acaa-20b7acd93156", "name": "Aba 25k a 50k", "credentials": {"googleSheetsOAuth2Api": {"id": "6VuUVtVrNszA1WU1", "name": "Google Sheets account"}}}, {"parameters": {"operation": "appendOrUpdate", "documentId": {"__rl": true, "value": "17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw", "mode": "list", "cachedResultName": "NOMES PRECATÓRIO", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw/edit?usp=drivesdk"}, "sheetName": {"__rl": true, "value": **********, "mode": "list", "cachedResultName": "NOMES PRECATÓRIOS ESTADUAIS", "cachedResultUrl": "https://docs.google.com/spreadsheets/d/17ef-DomklLe_BavGrnrVwIhUHNBBI1l5G0MGX9x5FWw/edit#gid=**********"}, "columns": {"mappingMode": "defineBelow", "value": {"Nº PROCESSO": "={{ $json['Nº PROCESSO (Completo)'] }}", " NOME": "={{ $json.Nome }}", "CPF": "={{ $json.CPF }}", "VALOR TOTAL": "={{ $json['VALOR TOTAL'] }}", "JUROS": "={{ $json.JUROS }}", "VALOR PRINCIPAL": "={{ $json['<PERSON><PERSON> Principal'] }}", "Ofício": "={{ $('Download File').item.json.file_name }}"}, "matchingColumns": ["Nº PROCESSO"], "schema": [{"id": " NOME", "displayName": " NOME", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Nº PROCESSO", "displayName": "Nº PROCESSO", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true, "removed": false}, {"id": "TELEFONE ", "displayName": "TELEFONE ", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "CPF", "displayName": "CPF", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "OBS", "displayName": "OBS", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "ÚLTIMA INTERAÇÃO", "displayName": "ÚLTIMA INTERAÇÃO", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "DATA", "displayName": "DATA", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "TELEFONE 1", "displayName": "TELEFONE 1", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "TELEFONE 2", "displayName": "TELEFONE 2", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "VALOR TOTAL", "displayName": "VALOR TOTAL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "JUROS", "displayName": "JUROS", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "VALOR PRINCIPAL", "displayName": "VALOR PRINCIPAL", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "<PERSON><PERSON><PERSON>", "displayName": "<PERSON><PERSON><PERSON>", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}, {"id": "Status Certidão", "displayName": "Status Certidão", "required": false, "defaultMatch": false, "display": true, "type": "string", "canBeUsedToMatch": true}], "attemptToConvertTypes": false, "convertFieldsToString": false}, "options": {}}, "type": "n8n-nodes-base.googleSheets", "typeVersion": 4.5, "position": [2460, 360], "id": "e4e2ef54-82cd-4b1b-add4-a4e70c2df508", "name": "Aba +50k", "retryOnFail": false, "credentials": {"googleSheetsOAuth2Api": {"id": "6VuUVtVrNszA1WU1", "name": "Google Sheets account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "a80813e9-b675-4527-83f9-fa4715ff1805", "leftValue": "={{ $json.doc_tipo }}", "rightValue": "CPF", "operator": {"type": "string", "operation": "equals", "name": "filter.operator.equals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1760, 280], "id": "721da235-065a-47a0-880d-f81143bdf713", "name": "Descarte CNPJ"}, {"parameters": {"jsCode": "/**\n *  Campos criados:\n *    cpf_cnpj          → somente dígitos ('' se inexistente)\n *    doc_tipo          → 'CPF' | 'CNPJ' | 'NC'\n *    certidao_status   → 'VERIFICAR' | 'INVÁLIDO'\n *    valor_numerico    → valor total convertido para número\n *    categoria_valor   → 'BAIXO' | 'MÉDIO' | 'ALTO'\n */\n\nconst toDigits = s => (s || '').replace(/\\D/g, '');\nconst padLeft  = (s, len) => s.padStart(len, '0');\nconst isNC     = s => !s || String(s).trim().toLowerCase() === 'n/c' || String(s).trim().toLowerCase() === 'nc';\n\n// Função para converter valor brasileiro para número\nconst parseValorBR = (valorStr) => {\n  if (!valorStr || isNC(valorStr)) return 0;\n  \n  // Remove R$, espaços, e converte vírgula para ponto\n  const numeroLimpo = String(valorStr)\n    .replace(/[R$\\s]/g, '')           // Remove R$ e espaços\n    .replace(/\\./g, '')              // Remove pontos (milhares)\n    .replace(',', '.');              // Converte vírgula para ponto (decimais)\n  \n  const numero = parseFloat(numeroLimpo);\n  return isNaN(numero) ? 0 : numero;\n};\n\n// Função para categorizar valor\nconst categorizarValor = (valor) => {\n  if (valor < 25000) return 'BAIXO';\n  if (valor < 50000) return 'MÉDIO';\n  return 'ALTO';\n};\n\nreturn $input.all().map(item => {\n\n  /* ---- 1. lê campos vindos do OpenAI -------------------- */\n  let content = (item.json.message ?? {}).content;\n  if (typeof content === 'string') {\n    try { content = JSON.parse(content); } catch { content = {}; }\n  }\n  const bas = content?.basico ?? {};\n  const val = content?.valores ?? {};\n\n  /* ---- 2. VALIDAÇÃO CPF --------------------------------- */\n  const cand1 = toDigits(bas.cpf_limpo ?? '');\n  const cand2 = toDigits(bas.cpf       ?? '');\n\n  // NC / vazio\n  if (isNC(bas.cpf_limpo) && isNC(bas.cpf)) {\n    item.json.cpf_cnpj        = '';\n    item.json.doc_tipo        = 'NC';\n    item.json.certidao_status = 'INVÁLIDO';\n  } else {\n    // escolhe valor mais longo\n    let numero = cand1.length >= cand2.length ? cand1 : cand2;\n\n    // completa zeros se faltarem (máx. 2)\n    if      (numero.length === 9 || numero.length === 10)  numero = padLeft(numero, 11);\n    else if (numero.length === 12 || numero.length === 13) numero = padLeft(numero, 14);\n\n    // classifica\n    let docTipo = 'NC';\n    let status  = 'INVÁLIDO';\n\n    if (numero.length === 11) {\n      docTipo = 'CPF';\n      status  = 'VERIFICAR';\n    } else if (numero.length === 14) {\n      docTipo = 'CNPJ';\n      status  = 'INVÁLIDO';\n    }\n\n    // grava CPF\n    item.json.cpf_cnpj        = numero;\n    item.json.doc_tipo        = docTipo;\n    item.json.certidao_status = status;\n  }\n\n  /* ---- 3. PROCESSAMENTO DE VALOR ------------------------ */\n  const valorTotal = val.total ?? '';\n  const valorNumerico = parseValorBR(valorTotal);\n  const categoria = categorizarValor(valorNumerico);\n\n  // grava valor\n  item.json.valor_numerico  = valorNumerico;\n  item.json.categoria_valor = categoria;\n\n  /* ---- 4. devolve item ---------------------------------- */\n  return item;\n});"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1620, 280], "id": "5069a620-5232-4490-b419-94cb1cf9d9e0", "name": "Valida CPF + Valor"}], "pinData": {"File Created": [{"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "11gjRxPb6r5BcyFxDVhLKjdP3SCtw58SK", "name": "doc_77540789.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=11gjRxPb6r5BcyFxDVhLKjdP3SCtw58SK&export=download", "webViewLink": "https://drive.google.com/file/d/11gjRxPb6r5BcyFxDVhLKjdP3SCtw58SK/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBNlHVAHLsWaMyu0gLbieykYHYGzp_q-nMaRyXTpYA1sYXgqBLveodw6zoAP6Cy4IPYDkKmIluuS9xnbT8hxCdsSTA8EedOzbV74shzHVL1zcQ=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:43:00.837Z", "createdTime": "2025-06-06T01:43:00.837Z", "modifiedTime": "2025-06-06T01:42:59.889Z", "modifiedByMeTime": "2025-06-06T01:42:59.889Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_77540789.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "dc2a53bea2545cc7b989ec47ce39a653", "sha1Checksum": "8bad48137970a99add47739a2b1fa5c005665912", "sha256Checksum": "d6982b484b9f8ce77337501c9e37d43e2f02719ca9ddeb1d882e9124ac511da8", "size": "33081", "quotaBytesUsed": "33081", "headRevisionId": "0B9sCNPnAtJYSdHphdDdvOHBUVDlmcStPU3FKeTVCS0NlQW0wPQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "1pOabw6vcs5OYgfYIcpYOAyHq50mKmTDW", "name": "doc_77542363.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=1pOabw6vcs5OYgfYIcpYOAyHq50mKmTDW&export=download", "webViewLink": "https://drive.google.com/file/d/1pOabw6vcs5OYgfYIcpYOAyHq50mKmTDW/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBM6oYhZCci1jzLxxqR9WrIXd-g0oRDKcZfSqDCobPGDKEm5-JW8rgxz3k1Ot8VdE6WPJnGLCYWXMurRjFdvq32wIC66yj0ltmGK3OM7p3LBXg=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:42:33.524Z", "createdTime": "2025-06-06T01:42:33.524Z", "modifiedTime": "2025-06-06T01:42:32.569Z", "modifiedByMeTime": "2025-06-06T01:42:32.569Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_77542363.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "5d37c7239794e9d9d6cc7f77022d2389", "sha1Checksum": "04ee3f65b19c482b65f4a495084df4093398520e", "sha256Checksum": "a3182de1746f3f28c9919559cd16533a4f887b89af1701612444caaf3ec97f1b", "size": "31978", "quotaBytesUsed": "31978", "headRevisionId": "0B9sCNPnAtJYSd201Q0RJb3I5bTBXU2V6MlhiNzdqLzZCdEh3PQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "1355qAdHLmm4yxTZuHkUUBgM7ycULHDmQ", "name": "doc_77540530.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=1355qAdHLmm4yxTZuHkUUBgM7ycULHDmQ&export=download", "webViewLink": "https://drive.google.com/file/d/1355qAdHLmm4yxTZuHkUUBgM7ycULHDmQ/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBNPebTlQS2vDS6wEBRu8YUY7ObpwwRupTNed69OBiPxi_2qBPmqHSSX86fuxAs1kVYdg2PNyded3Z5LvfrK6CvoPQ9OahN2mt4sRoIfIso65Q=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:42:05.100Z", "createdTime": "2025-06-06T01:42:05.100Z", "modifiedTime": "2025-06-06T01:42:04.151Z", "modifiedByMeTime": "2025-06-06T01:42:04.151Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_77540530.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "b4e613f13c9652798d04edcf1d397bc0", "sha1Checksum": "a59088264aaa0f5b6bda6439f3072000d24f6ec2", "sha256Checksum": "f0a7b01a5325805a9068ff32107ee19d122b36ba246f312aebf51596dde5dc97", "size": "37936", "quotaBytesUsed": "37936", "headRevisionId": "0B9sCNPnAtJYSUmpaQkZybGNIUk1zT0JrNHlES3h6Tno2dlRJPQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "1DgFDXkVn3gCeahRcV8e-W8syT-G6cWUc", "name": "doc_77541152.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=1DgFDXkVn3gCeahRcV8e-W8syT-G6cWUc&export=download", "webViewLink": "https://drive.google.com/file/d/1DgFDXkVn3gCeahRcV8e-W8syT-G6cWUc/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBOpHwT_gQmQPxtb5XUfByAbXCeY4nXTgEXfhK2nB6mXeIECRKt6uwcBEPPVbCA5kCk9EgoffilfDIQXS9ANf4NINEMP04EUBuHkh52oZVKA-g=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:41:18.814Z", "createdTime": "2025-06-06T01:41:18.814Z", "modifiedTime": "2025-06-06T01:41:17.855Z", "modifiedByMeTime": "2025-06-06T01:41:17.855Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_77541152.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "67e1e87f0d3cd2fbbce50e51fababc3b", "sha1Checksum": "fb4fe1d85f888d586c4788af97b451a8c7c35f2a", "sha256Checksum": "bfb36dfb90ce83adda2b72d8e38057838df5e63892da2482a11a9e154a27420e", "size": "32389", "quotaBytesUsed": "32389", "headRevisionId": "0B9sCNPnAtJYSc3RvVUU4VENZQVpoWEdhbEF5YkU5SnV0SGtFPQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "1Ti1tbh0ITC1OND3BNiRAm5s6cOdX2Ad2", "name": "doc_77540359.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=1Ti1tbh0ITC1OND3BNiRAm5s6cOdX2Ad2&export=download", "webViewLink": "https://drive.google.com/file/d/1Ti1tbh0ITC1OND3BNiRAm5s6cOdX2Ad2/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBOz89KtkVk_10kOmSXJb4FAB9nDxrm-oWBjwr67At30DKYHUil9qwewWvhJJJcs8axWyVxaGJTGwX6SeLvtlauK_jXpi3AEqC3QyCIQdFYAFw=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:41:00.204Z", "createdTime": "2025-06-06T01:41:00.204Z", "modifiedTime": "2025-06-06T01:40:59.255Z", "modifiedByMeTime": "2025-06-06T01:40:59.255Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_77540359.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "af36a57101cdcfc611dacad77e05918d", "sha1Checksum": "0104c3a84e4a925c73de4d91adb67066be053f34", "sha256Checksum": "dfd2fee72e9e00469f8d1471b144d0ed16be97bf6f4e8064c341a4d07e89e42a", "size": "37821", "quotaBytesUsed": "37821", "headRevisionId": "0B9sCNPnAtJYSSlZ4MWZ6TStyM2x1TFJEdVJWR3R4K0pUNEhzPQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "18W3QSJ7FmFw9Ie_l454_sAe9CndJ33ne", "name": "doc_77540665.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=18W3QSJ7FmFw9Ie_l454_sAe9CndJ33ne&export=download", "webViewLink": "https://drive.google.com/file/d/18W3QSJ7FmFw9Ie_l454_sAe9CndJ33ne/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBOpi9NaNtOD2cPuh4iBLpkC3_KD1UJ2M0B4wTFbNp2GZZrGs5sjQBLWW1RljaOLM34SnpowbDZzcJscHg21hgKlW1lZ-Z7qiQFqyAOLIpmsow=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:40:30.265Z", "createdTime": "2025-06-06T01:40:30.265Z", "modifiedTime": "2025-06-06T01:40:29.311Z", "modifiedByMeTime": "2025-06-06T01:40:29.311Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_77540665.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "15e5e1dac54aeb6ff309808cb958b938", "sha1Checksum": "87c4ce9f7ab77db3ce3bae8c954beeeaed8cf311", "sha256Checksum": "8d27e27934acff96586bacad4993226d5a2f2472c8bc0012c9c332f31f8adb8f", "size": "32385", "quotaBytesUsed": "32385", "headRevisionId": "0B9sCNPnAtJYSTlIzeXVMWW1XdkNZRS9mNjBLM1dKNWFzQy9NPQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "1pYcSnlhFOUMiq5McRf36QQZQltEfh03x", "name": "doc_77540109.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=1pYcSnlhFOUMiq5McRf36QQZQltEfh03x&export=download", "webViewLink": "https://drive.google.com/file/d/1pYcSnlhFOUMiq5McRf36QQZQltEfh03x/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBOy1rinmBSlNp4obatxm_BpXAbFJ-2a3srK43A6AkAe4duCz4UMSFkQuIwWr2TVKrRlpdbLPhDK2AOitCwp1W7usJWhkO3KA5FqoQ1qhj2t4A=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:40:11.129Z", "createdTime": "2025-06-06T01:40:11.129Z", "modifiedTime": "2025-06-06T01:40:10.189Z", "modifiedByMeTime": "2025-06-06T01:40:10.189Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_77540109.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "23f0000289d83481c5e7f6ce5beb6749", "sha1Checksum": "0624b951c384aeb37f53677a35fa8bfc0e10f7c1", "sha256Checksum": "448e5798d42c503a1bd7c870e3ac8051b6c993cec97bec4d26fac5688c13a5ac", "size": "37901", "quotaBytesUsed": "37901", "headRevisionId": "0B9sCNPnAtJYSdWNLVW5KMUltVTZ2L3FxVHQ1QnpwTDBNcXc0PQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "1qWEJb4nnecuDHoTYkADGNbvC-tb8YOsl", "name": "doc_77540148.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=1qWEJb4nnecuDHoTYkADGNbvC-tb8YOsl&export=download", "webViewLink": "https://drive.google.com/file/d/1qWEJb4nnecuDHoTYkADGNbvC-tb8YOsl/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBNvjQtHXOSFNbg3EQuHvHjQNlsTJBAZOSzGEXZ3LXs6BY_4EhlFTBRtVuy_hTnzEGkqQp65n3gQ1bv_dDmO5k9ufz67B4q1P8OcuM6hXQacEQ=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:39:51.805Z", "createdTime": "2025-06-06T01:39:51.805Z", "modifiedTime": "2025-06-06T01:39:50.849Z", "modifiedByMeTime": "2025-06-06T01:39:50.849Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_77540148.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "beff38d5edc730e6f85fe4b4be6ced36", "sha1Checksum": "9b6c838e6939b98e27adb9dae3121c162bedf9ac", "sha256Checksum": "ef77051ced2fac0ac8e73fc8ca93c9f9c137fe3f7508fa25734205c8c8cfcf61", "size": "46598", "quotaBytesUsed": "46598", "headRevisionId": "0B9sCNPnAtJYSNUpDekFqWEE3dTBaNERzcEUwNzdDYVZDb0tjPQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "1NrnLz944JVGdxbiggY4ohK9VUPL8Os1Y", "name": "doc_77540231.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=1NrnLz944JVGdxbiggY4ohK9VUPL8Os1Y&export=download", "webViewLink": "https://drive.google.com/file/d/1NrnLz944JVGdxbiggY4ohK9VUPL8Os1Y/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBMLnNrGyzNLTUxQFtJZ0Naynv7sE8THWp26nctmYC80Mx_MXpq5peovCDC1HCOZ0SbfuYw0j9Ye62RKkc8qJEiX-A94cE7TDzXGECKcbC0Rdw=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:39:32.907Z", "createdTime": "2025-06-06T01:39:32.907Z", "modifiedTime": "2025-06-06T01:39:31.953Z", "modifiedByMeTime": "2025-06-06T01:39:31.953Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_77540231.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "f2ac3b739cae4d28a4c1dee55b209478", "sha1Checksum": "f3aa122ce16acfee3c0ce56657f45a39c0526514", "sha256Checksum": "6dbcea47fcfb81ca1f5027087863cbb0e6ca8d94960ab160726a0135d3049be8", "size": "37937", "quotaBytesUsed": "37937", "headRevisionId": "0B9sCNPnAtJYSMWxrN1h4bE9kN0J2cGl4Y2ljSWl1cHZEa2ljPQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "1z2U3EDRC9NoF5WrewZGKhmBK5lUIZnST", "name": "doc_77539830.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=1z2U3EDRC9NoF5WrewZGKhmBK5lUIZnST&export=download", "webViewLink": "https://drive.google.com/file/d/1z2U3EDRC9NoF5WrewZGKhmBK5lUIZnST/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBMBwADW68TX5ab16ch9sNBgJHf4MrTUJ1Tru0XS_T6PLTX0IlFzBfshvkF4x3za8cvU0oVcJf4UFR_4BfHP16NtYtH--ItUY6GlCPtzArcRaQ=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:39:08.489Z", "createdTime": "2025-06-06T01:39:08.489Z", "modifiedTime": "2025-06-06T01:39:07.527Z", "modifiedByMeTime": "2025-06-06T01:39:07.527Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_77539830.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "950e47866008c8f745a26e2817872cd7", "sha1Checksum": "c05259cab859a180a405666d5e9f2be9f38fc9f6", "sha256Checksum": "b3f326ef5553778669178259668249c3ac7773acaacbd2d7a08efe9cab9aaa9d", "size": "57671", "quotaBytesUsed": "57671", "headRevisionId": "0B9sCNPnAtJYSRm53OG9JMVI4UVZYVWdQYklKWE90NFZHSExJPQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "1gxepeCACUtBWBQL3NZvS6DMDW_b93bjE", "name": "doc_77526659.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=1gxepeCACUtBWBQL3NZvS6DMDW_b93bjE&export=download", "webViewLink": "https://drive.google.com/file/d/1gxepeCACUtBWBQL3NZvS6DMDW_b93bjE/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBN9bG4o1TeKWgAOjeFkLOTPWEZqgS_ZF6SxgCah9iQjp5neOCE_5kbMWa8-mZXx4FCITsX-55HuQnFEg2SbecZg2JgKSqJPoIhiG5YJN1JYFg=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:37:50.706Z", "createdTime": "2025-06-06T01:37:50.706Z", "modifiedTime": "2025-06-06T01:37:49.747Z", "modifiedByMeTime": "2025-06-06T01:37:49.747Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_77526659.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "24e5869617667f3fb8f05a38c8e5ab9c", "sha1Checksum": "405dc1ba0cff4f13a888751d8dbfe96ffdc31843", "sha256Checksum": "bb0c81200e1ae90802f913e516dd02eacf54a818c66e2de316e326233f9c834f", "size": "49024", "quotaBytesUsed": "49024", "headRevisionId": "0B9sCNPnAtJYScVU3dkFQejA2UFdZVmx6ajZyQlYvRWhHREJrPQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "1ney9BfVS79J0PqXt8FaTg487bQsfzUR9", "name": "doc_77526529.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=1ney9BfVS79J0PqXt8FaTg487bQsfzUR9&export=download", "webViewLink": "https://drive.google.com/file/d/1ney9BfVS79J0PqXt8FaTg487bQsfzUR9/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBNojktw7uGB82Tu3KzIY9tNFg4WaoMFHUGnC5DyzMq0uux95AOBHyDLofE99OCYKNalUWfRpr5uRehSzKCKtZuJACZhx8Gzqt_H4Mx0SeXdug=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:37:31.963Z", "createdTime": "2025-06-06T01:37:31.963Z", "modifiedTime": "2025-06-06T01:37:31.011Z", "modifiedByMeTime": "2025-06-06T01:37:31.011Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_77526529.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "42eb8591a0fb9750965aff08cbde20a4", "sha1Checksum": "b58214dce96f4b743f9a1380986ed209c4251bbf", "sha256Checksum": "1e29bddd30e1a50dd81f2f30f6c34eebf8c28f9b5007d3e5546148645d08edea", "size": "48153", "quotaBytesUsed": "48153", "headRevisionId": "0B9sCNPnAtJYSWnF4WVdjbnZwaGtRUVRGam9udzVJWW4vUWx3PQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "1EJ6NR9f4g2WjisQzA0SZyKio87Dn9tWc", "name": "doc_26068640.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=1EJ6NR9f4g2WjisQzA0SZyKio87Dn9tWc&export=download", "webViewLink": "https://drive.google.com/file/d/1EJ6NR9f4g2WjisQzA0SZyKio87Dn9tWc/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBNqS86WbLBf80c7q36ftClQyYYYmbSMzGbJ9_ntg1Nq7Nvnwc9F1kZ9aQlekIpF4hcA3MHDrY_poVNtuc9Y7j20G-OdEyWX4cBSLylE7N2EDw=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:34:06.947Z", "createdTime": "2025-06-06T01:34:06.947Z", "modifiedTime": "2025-06-06T01:34:06.004Z", "modifiedByMeTime": "2025-06-06T01:34:06.004Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_26068640.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "c44c006505561e81550776c552688f52", "sha1Checksum": "69be7ff148a5aa5d293d109254327368379da102", "sha256Checksum": "b8334130a128be6643760ad8660b79f58e54fced21aacd0a13ad6003f179dff5", "size": "29731", "quotaBytesUsed": "29731", "headRevisionId": "0B9sCNPnAtJYSbmF0SWZTUTZ0ZXk1S2hxaWZRbzNac2pQV2xJPQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "1rrnFrXaIifL-D25iBZYM9PRzwoClbDVH", "name": "doc_77439635.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=1rrnFrXaIifL-D25iBZYM9PRzwoClbDVH&export=download", "webViewLink": "https://drive.google.com/file/d/1rrnFrXaIifL-D25iBZYM9PRzwoClbDVH/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBMw-wCcKqM9qS6HV5kj4mphmMawCdD2NlAFpNiis7sHTBqm7FmzMDBPacsrqp3_zna4eUN2M9Pk8v_jyFFA19GC-8pAD-xKj-gdDYE0rCZbpA=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:31:48.381Z", "createdTime": "2025-06-06T01:31:48.381Z", "modifiedTime": "2025-06-06T01:31:47.421Z", "modifiedByMeTime": "2025-06-06T01:31:47.421Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_77439635.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "49e1a3e648a94ff5a127bad216265f38", "sha1Checksum": "ce6cceaa8f9f8c0b06a86334db709a0a4de587dd", "sha256Checksum": "6df0c83a978dbf5e5b4d8e5b1748afb853b046626ee062ef40d7b6c3606a0259", "size": "32017", "quotaBytesUsed": "32017", "headRevisionId": "0B9sCNPnAtJYSNktQcTNua1Z1MlFuVk1lOU9SZ3hMWVZyNTM0PQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "17l_hX_-hXOz8iCzZQyWmuEFSpKoc8oEp", "name": "doc_77439535.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=17l_hX_-hXOz8iCzZQyWmuEFSpKoc8oEp&export=download", "webViewLink": "https://drive.google.com/file/d/17l_hX_-hXOz8iCzZQyWmuEFSpKoc8oEp/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBPkAa1GaJ2Sl7_h9iI4b922AjRjE2hu33hMjgmfXg7uDXvIRj_PX3Pxewy24bCtOqnJWB-x4EsrUTG_7ejXirvh7q-W6eTDlJ1IxsOonGJmzQ=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:31:29.708Z", "createdTime": "2025-06-06T01:31:29.708Z", "modifiedTime": "2025-06-06T01:31:28.755Z", "modifiedByMeTime": "2025-06-06T01:31:28.755Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_77439535.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "49059aa128ad23dfc964da31e7e82b98", "sha1Checksum": "4d3f14e4a5390e25bacc632b88477f18b8a2a685", "sha256Checksum": "06a9fb3c1028f2727f2e70a1888f18ab2e588cd5fe152e9d001cdf19863ff48c", "size": "31952", "quotaBytesUsed": "31952", "headRevisionId": "0B9sCNPnAtJYSc2FRb3lZWU84RHBzSk4rT0xha2p6R29EUi9BPQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}, {"json": {"parents": ["1DNDwV--bCVZH1q6BUn4mrNPqPvWOkeVZ"], "lastModifyingUser": {"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}, "owners": [{"displayName": "<PERSON><PERSON>re <PERSON>", "kind": "drive#user", "me": true, "permissionId": "16614508434093362325", "emailAddress": "<EMAIL>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64"}], "permissions": [{"kind": "drive#permission", "id": "anyoneWithLink", "type": "anyone", "role": "writer", "allowFileDiscovery": false}, {"kind": "drive#permission", "id": "16614508434093362325", "type": "user", "emailAddress": "<EMAIL>", "role": "owner", "displayName": "<PERSON><PERSON>re <PERSON>", "photoLink": "https://lh3.googleusercontent.com/a/ACg8ocI1s7V3HyTEwLDXUjxaJThhY-ybMFDQBRQG0AEEPNMeuTX9fZs=s64", "deleted": false, "pendingOwner": false}], "spaces": ["drive"], "capabilities": {"canAcceptOwnership": false, "canAddChildren": false, "canAddMyDriveParent": false, "canChangeCopyRequiresWriterPermission": true, "canChangeSecurityUpdateEnabled": false, "canChangeViewersCanCopyContent": true, "canComment": true, "canCopy": true, "canDelete": true, "canDisableInheritedPermissions": false, "canDownload": true, "canEdit": true, "canEnableInheritedPermissions": true, "canListChildren": false, "canModifyContent": true, "canModifyContentRestriction": true, "canModifyEditorContentRestriction": true, "canModifyOwnerContentRestriction": true, "canModifyLabels": false, "canMoveChildrenWithinDrive": false, "canMoveItemIntoTeamDrive": true, "canMoveItemOutOfDrive": true, "canMoveItemWithinDrive": true, "canReadLabels": false, "canReadRevisions": true, "canRemoveChildren": false, "canRemoveContentRestriction": false, "canRemoveMyDriveParent": true, "canRename": true, "canShare": true, "canTrash": true, "canUntrash": true}, "permissionIds": ["anyoneWithLink", "16614508434093362325"], "linkShareMetadata": {"securityUpdateEligible": false, "securityUpdateEnabled": true}, "kind": "drive#file", "id": "1fMm2fMgwBO2ewSfKS1UFnXg-dBg4z_aU", "name": "doc_23117901.pdf", "mimeType": "application/pdf", "starred": false, "trashed": false, "explicitlyTrashed": false, "version": "3", "webContentLink": "https://drive.google.com/uc?id=1fMm2fMgwBO2ewSfKS1UFnXg-dBg4z_aU&export=download", "webViewLink": "https://drive.google.com/file/d/1fMm2fMgwBO2ewSfKS1UFnXg-dBg4z_aU/view?usp=drivesdk", "iconLink": "https://drive-thirdparty.googleusercontent.com/16/type/application/pdf", "hasThumbnail": true, "thumbnailLink": "https://lh3.googleusercontent.com/drive-storage/AJQWtBP8Hy5MZbzJF4cYgjuv50wilYF-XFD_uDJZGewJUjxTUOblRQ-ofo3hRwmL_mp1lomJawVTnPsIhPgq2bKah1jgYUt1rPeYIdIRrPh82yX8Eg=s220", "thumbnailVersion": "1", "viewedByMe": true, "viewedByMeTime": "2025-06-06T01:28:20.046Z", "createdTime": "2025-06-06T01:28:20.046Z", "modifiedTime": "2025-06-06T01:28:19.091Z", "modifiedByMeTime": "2025-06-06T01:28:19.091Z", "modifiedByMe": true, "shared": true, "ownedByMe": true, "viewersCanCopyContent": true, "copyRequiresWriterPermission": false, "writersCanShare": true, "originalFilename": "doc_23117901.pdf", "fullFileExtension": "pdf", "fileExtension": "pdf", "md5Checksum": "6a63550ce1284ea4368b4fb090c5b4c6", "sha1Checksum": "8fb22f191f10e3853e3901054b5e275fa34b5478", "sha256Checksum": "862137d73335b0562b0ac65836c1143d69247dc3e54bf8b14b73e889208d33bc", "size": "30739", "quotaBytesUsed": "30739", "headRevisionId": "0B9sCNPnAtJYSUmdtcGxtWG4wQ25hS2VzUlpjempjb29yNTlRPQ", "isAppAuthorized": false, "inheritedPermissionsDisabled": false}}]}, "connections": {"Download File": {"main": [[{"node": "Switch", "type": "main", "index": 0}]]}, "File Created": {"main": [[{"node": "Set File ID", "type": "main", "index": 0}]]}, "File Updated": {"main": [[{"node": "Set File ID", "type": "main", "index": 0}]]}, "Set File ID": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Extract PDF Text": {"main": [[{"node": "OpenAI", "type": "main", "index": 0}]]}, "Switch": {"main": [[{"node": "Extract PDF Text", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}], [{"node": "No Operation, do nothing", "type": "main", "index": 0}]]}, "Loop Over Items": {"main": [[{"node": "No Operation, do nothing1", "type": "main", "index": 0}], [{"node": "Download File", "type": "main", "index": 0}]]}, "OpenAI": {"main": [[{"node": "Valida CPF + Valor", "type": "main", "index": 0}]]}, "Wait": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Loop Over Items", "type": "main", "index": 0}]]}, "No Operation, do nothing": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Enriquecimento Tel": {"main": [[{"node": "Base de Dados", "type": "main", "index": 0}]]}, "Base de Dados": {"main": [[{"node": "Filtro de Valores", "type": "main", "index": 0}]]}, "Filtro de Valores": {"main": [[{"node": "Aba -25k", "type": "main", "index": 0}], [{"node": "Aba 25k a 50k", "type": "main", "index": 0}], [{"node": "Aba +50k", "type": "main", "index": 0}]]}, "Aba -25k": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Aba 25k a 50k": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Aba +50k": {"main": [[{"node": "Wait", "type": "main", "index": 0}]]}, "Descarte CNPJ": {"main": [[{"node": "Enriquecimento Tel", "type": "main", "index": 0}], [{"node": "Wait", "type": "main", "index": 0}]]}, "Valida CPF + Valor": {"main": [[{"node": "Descarte CNPJ", "type": "main", "index": 0}]]}}, "active": true, "settings": {"executionOrder": "v1", "callerPolicy": "workflowsFromSameOwner", "errorWorkflow": "G7f94vvatV1NSouY"}, "versionId": "83b5099b-1305-4ed2-8444-b4ec9d60162f", "meta": {"templateCredsSetupCompleted": true, "instanceId": "6c940b404fd1bbd74632faf15dd34a3093ba35daa7ce2eb1d46c850d77b1f8a9"}, "id": "sAQnESbNyrDmRkb0", "tags": []}