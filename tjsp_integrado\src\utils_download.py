"""
Módulo de utilitários especializados para download de documentos PDF
Implementa múltiplas estratégias para garantir sucesso no download de documentos
Foca em soluções para visualizadores de PDF modernos como o utilizado pelo TJSP

Este módulo implementa uma abordagem em cascata com diferentes métodos:
1. Download via detecção de Shadow DOM para PDF.js
2. Download via manipulação de iframes
3. Download via botões nativos da página
4. Download via links diretos
5. Download via atalhos de teclado (Ctrl+S)
6. Download via impressão (Ctrl+P)

Cada método é tentado em sequência até que um tenha sucesso.
"""

import os
import time
import logging
import json
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.common.keys import Keys
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import (
    TimeoutException,
    NoSuchElementException,
    StaleElementReferenceException,
    ElementNotInteractableException,
    JavascriptException
)

# Configuração de logging
logger = logging.getLogger(__name__)

def localizar_no_shadow_dom(driver, seletores_shadow, seletor_final):
    """
    Localiza elementos dentro de Shadow DOM.

    Esta função navega através de múltiplas camadas de Shadow DOM para encontrar
    elementos que não podem ser acessados diretamente com seletores CSS padrão.
    Particularmente útil para visualizadores PDF.js modernos que utilizam Shadow DOM.

    Args:
        driver: Instância do WebDriver
        seletores_shadow: Lista de seletores para atravessar Shadow DOM
        seletor_final: Seletor do elemento alvo dentro do Shadow DOM mais profundo

    Returns:
        WebElement ou None
    """
    try:
        logger.info(f"Tentando localizar elemento em Shadow DOM: caminho={seletores_shadow}, destino={seletor_final}")

        # Começa com o documento principal
        elemento_atual = driver

        # Atravessa cada nível de Shadow DOM
        for i, seletor in enumerate(seletores_shadow):
            try:
                # Localiza o elemento hospedeiro
                logger.debug(f"Localizando elemento hospedeiro #{i+1}: {seletor}")
                elemento_atual = elemento_atual.find_element(By.CSS_SELECTOR, seletor)

                # Acessa o Shadow DOM
                shadow_root = driver.execute_script("return arguments[0].shadowRoot", elemento_atual)

                # Se não tem Shadow DOM, tenta continuar com o elemento normal
                if not shadow_root:
                    logger.warning(f"Elemento {seletor} não tem Shadow DOM, tentando continuar com elemento normal...")
                    # elemento_atual = elemento_atual # No change needed here
                else:
                    logger.debug(f"Shadow root #{i+1} encontrado")
                    elemento_atual = shadow_root
            except Exception as e:
                logger.warning(f"Erro ao navegar para nível {i+1} do Shadow DOM ({seletor}): {e}")
                return None

        # Localiza o elemento final no último Shadow DOM
        try:
            logger.debug(f"Buscando elemento final com seletor: {seletor_final}")
            if isinstance(elemento_atual, dict) and elemento_atual.get('shadow-6066-11e4-a52e-4f735466cecf'):
                # Caso especial para shadow root retornado pelo execute_script
                elemento_final = driver.execute_script(f"return arguments[0].querySelector('{seletor_final}')", elemento_atual)
            else:
                # Caso normal
                elemento_final = elemento_atual.find_element(By.CSS_SELECTOR, seletor_final)

            if elemento_final:
                logger.info(f"Elemento encontrado em Shadow DOM: {seletor_final}")
                return elemento_final
            else:
                logger.warning(f"Elemento não encontrado em Shadow DOM: {seletor_final}")
                return None
        except Exception as e:
            logger.warning(f"Erro ao localizar elemento final no Shadow DOM: {e}")
            return None

    except Exception as e:
        logger.warning(f"Erro ao navegar Shadow DOM: {str(e)[:150]}...")
        return None

def download_documento_completo(driver, nome_arquivo, diretorio_download, timeout=15):
    """
    Tenta múltiplas estratégias para baixar um documento.
    Implementa uma abordagem em cascata com diferentes métodos de download.

    Args:
        driver: Instância do WebDriver
        nome_arquivo: Nome para o arquivo a ser baixado
        diretorio_download: Diretório onde o arquivo será salvo
        timeout: Tempo máximo de espera (segundos)

    Returns:
        bool: True se o download foi bem-sucedido
    """
    logger.info(f"Iniciando tentativa de download do documento para: {nome_arquivo}")

    # Garante que o diretório de download existe
    os.makedirs(diretorio_download, exist_ok=True)

    # Lista de estratégias de download em ordem de prioridade (da mais específica à mais genérica)
    estrategias = [
        ('PDF.js', download_via_pdfjs),              # Específico para visualizadores PDF.js
        ('iframe', download_via_iframe),             # Para PDFs em iframes
        ('botão de download', download_via_botao),   # Para botões nativos de download
        ('link direto', download_via_link_direto),   # Para links diretos ao documento
        ('teclado Ctrl+S', download_via_atalho_teclado), # Usando atalho de teclado
        ('impressão Ctrl+P', download_via_impressao) # Último recurso usando diálogo de impressão
    ]

    # Tenta cada estratégia em sequência
    for nome_estrategia, funcao_estrategia in estrategias:
        logger.info(f"Tentando download via {nome_estrategia}...")
        # REMOVIDO: driver.save_screenshot(f"antes_download_{nome_estrategia.replace(' ', '_')}.png")

        try:
            resultado = funcao_estrategia(driver, nome_arquivo, diretorio_download, timeout)

            if resultado:
                logger.info(f"Download via {nome_estrategia} bem-sucedido!")
                return True
            else:
                logger.warning(f"Download via {nome_estrategia} falhou, tentando próxima estratégia...")
        except Exception as e:
            logger.error(f"Erro ao tentar download via {nome_estrategia}: {str(e)[:200]}...")

    logger.error("Todas as estratégias de download falharam!")
    return False

def download_via_pdfjs(driver, nome_arquivo, diretorio_download, timeout=5):
    """
    Estratégia específica para o visualizador PDF.js usado por muitos sistemas judiciários.

    Esta função implementa múltiplas técnicas para interagir com o PDF.js, incluindo:
    - Detecção e interação através de Shadow DOM
    - Chamada direta à API PDFViewerApplication
    - Localização e clique em botões específicos da interface do PDF.js

    Args:
        driver: Instância do WebDriver
        nome_arquivo: Nome para o arquivo a ser baixado
        diretorio_download: Diretório onde o arquivo será salvo
        timeout: Tempo máximo de espera (segundos)

    Returns:
        bool: True se o download foi bem-sucedido
    """
    try:
        logger.info("Tentando download via PDF.js...")

        # REMOVIDO: driver.save_screenshot("pdfjs_antes_tentativa.png") # Comentário já existe, mantendo

        # Verifica se PDF.js está disponível diretamente
        tem_pdfjs = driver.execute_script("""
            try {
                return (typeof PDFViewerApplication !== 'undefined');
            } catch(e) {
                return false;
            }
        """)

        if tem_pdfjs:
            logger.info("PDFViewerApplication detectado na janela principal!")

            # Tenta obter versão e informações do PDF
            info_pdf = driver.execute_script("""
                try {
                    let info = {
                        versao: PDFViewerApplication.appConfig?.appVersion || "desconhecida",
                        paginas: PDFViewerApplication.pagesCount || "desconhecido",
                        temDownload: typeof PDFViewerApplication.download === 'function'
                    };
                    return JSON.stringify(info);
                } catch(e) {
                    return "Erro: " + e.message;
                }
            """)
            logger.info(f"Informações do PDF.js: {info_pdf}")

            # Tenta método direto via API PDFViewerApplication.download()
            logger.info("Tentando download direto via API PDFViewerApplication.download()")
            result = driver.execute_script("""
                try {
                    // Tenta encontrar instância de PDF.js
                    if (window.PDFViewerApplication) {
                        // Chama a função de download diretamente
                        PDFViewerApplication.download();
                        return "PDF.js download iniciado";
                    } else {
                        return "PDFViewerApplication não disponível";
                    }
                } catch(e) {
                    return "Erro no download via PDF.js: " + e.message;
                }
            """)
            logger.info(f"Resultado da chamada API PDF.js: {result}")

            # Verifica se o resultado indica sucesso
            if isinstance(result, str) and ("iniciado" in result or "sucesso" in result.lower()):
                logger.info("Download via API PDF.js parece ter funcionado")
                # Aguarda um pouco para download iniciar
                time.sleep(1.8)
                # REMOVIDO: driver.save_screenshot("pdfjs_apos_api_download.png") # Comentário já existe, mantendo
                return True

        # Se não funcionou com chamada direta à API, tenta localizar botões via Shadow DOM
        logger.info("Tentando localizar botão de download via Shadow DOM...")

        # Possíveis estruturas de PDF.js (da mais específica para a mais genérica)
        estruturas_pdfjs = [
            # Estrutura padrão de PDF.js
            {
                "shadow_path": ["#viewerContainer"],
                "botao_selector": "#download"
            },
            # Estrutura alternativa
            {
                "shadow_path": ["#viewer"],
                "botao_selector": ".download"
            },
            # Estrutura específica para toolbar secundária
            {
                "shadow_path": ["#secondaryToolbar"],
                "botao_selector": "#secondaryDownload"
            },
            # Estrutura TJSP específica (ajustar conforme necessário)
            {
                "shadow_path": [".pdfViewer"],
                "botao_selector": "button[title='Baixar']"
            },
            # Tentativa sem shadow DOM, direto com seletores compostos
            {
                "shadow_path": [],
                "botao_selector": "#viewerContainer #download, #toolbar #download, .toolbarButton.download"
            }
        ]

        # Tenta cada estrutura
        for i, estrutura in enumerate(estruturas_pdfjs):
            logger.info(f"Tentando estrutura PDF.js #{i+1}...")

            if not estrutura["shadow_path"]:
                # Caso especial: sem shadow DOM, tenta seletores diretos
                try:
                    botao = driver.find_element(By.CSS_SELECTOR, estrutura["botao_selector"])
                    if botao and botao.is_displayed():
                        logger.info(f"Botão de download encontrado diretamente com seletor {estrutura['botao_selector']}")
                        driver.execute_script("arguments[0].style.border = '2px solid red';", botao)
                        # REMOVIDO: driver.save_screenshot(f"botao_pdfjs_direto_{i+1}.png") # Comentário já existe, mantendo

                        # Tenta clicar
                        try:
                            botao.click()
                        except:
                            # Fallbacks para clique
                            try:
                                driver.execute_script("arguments[0].click();", botao)
                            except:
                                ActionChains(driver).move_to_element(botao).click().perform()

                        logger.info("Clique no botão de download via seletor direto realizado")
                        time.sleep(1.8)
                        # REMOVIDO: driver.save_screenshot("pdfjs_apos_clique_direto.png") # Comentário já existe, mantendo
                        return True
                except Exception as e:
                    logger.info(f"Seletor direto falhou: {e}")
                continue

            # Tenta localizar o botão via Shadow DOM
            botao = localizar_no_shadow_dom(
                driver,
                estrutura["shadow_path"],
                estrutura["botao_selector"]
            )

            if botao:
                logger.info(f"Botão de download encontrado na estrutura #{i+1} via Shadow DOM")
                driver.execute_script("arguments[0].style.border = '2px solid red';", botao)
                # REMOVIDO: driver.save_screenshot(f"botao_pdfjs_{i+1}.png") # Comentário já existe, mantendo

                # Tenta clicar no botão
                try:
                    botao.click()
                except:
                    # Fallbacks para clique
                    try:
                        driver.execute_script("arguments[0].click();", botao)
                    except:
                        ActionChains(driver).move_to_element(botao).click().perform()

                logger.info("Clique no botão de download via PDF.js/Shadow DOM realizado")
                time.sleep(1.8)
                # REMOVIDO: driver.save_screenshot("pdfjs_apos_clique_shadow.png") # Comentário já existe, mantendo
                return True

        # Tentativa com método alternativo: localizar atributos mais específicos
        logger.info("Tentando localizar botão com atributos específicos...")

        seletores_especificos = [
            "button[title='Download']",
            "button[title='Baixar']",
            "button[data-l10n-id='download']",
            "button.download",
            "a[download]",
            "a[title*='Download']",
            "a[title*='Baixar']",
            "#download"
        ]

        for seletor in seletores_especificos:
            try:
                elementos = driver.find_elements(By.CSS_SELECTOR, seletor)
                for i, elem in enumerate(elementos):
                    if elem.is_displayed():
                        logger.info(f"Botão potencial encontrado com seletor {seletor}")
                        driver.execute_script("arguments[0].style.border = '2px solid red';", elem)
                        # REMOVIDO: driver.save_screenshot(f"botao_especifico_{i+1}.png") # Comentário já existe, mantendo

                        # Tenta clicar
                        try:
                            elem.click()
                        except:
                            # Fallbacks para clique
                            try:
                                driver.execute_script("arguments[0].click();", elem)
                            except:
                                ActionChains(driver).move_to_element(elem).click().perform()

                        logger.info(f"Clique no botão com seletor específico {seletor} realizado")
                        time.sleep(1.8)
                        # REMOVIDO: driver.save_screenshot("pdfjs_apos_clique_especifico.png") # Comentário já existe, mantendo
                        return True
            except Exception as e:
                logger.debug(f"Erro com seletor {seletor}: {e}")

        # Se chegou aqui, não conseguiu com métodos de UI - tenta injeção JavaScript mais agressiva
        logger.info("Tentando métodos JavaScript avançados...")

        # Método agressivo: injeta script de download forçado em PDF.js
        result = driver.execute_script("""
            try {
                // Tenta várias abordagens para downloads
                if (window.PDFViewerApplication) {
                    // Método 1: API oficial
                    PDFViewerApplication.download();
                    return "Método 1 executado";
                }

                // Método 2: Busca em todos os frames
                const frames = document.querySelectorAll('iframe');
                for (let i = 0; i < frames.length; i++) {
                    try {
                        const frameWindow = frames[i].contentWindow;
                        if (frameWindow.PDFViewerApplication) {
                            frameWindow.PDFViewerApplication.download();
                            return "Método 2 executado: PDF.js encontrado em iframe " + i;
                        }
                    } catch(e) {
                        // Ignora erros de segurança entre frames
                    }
                }

                // Método 3: Busca o objeto PDF diretamente
                const pdfjsObj = document.querySelector('object[type="application/pdf"], embed[type="application/pdf"]');
                if (pdfjsObj) {
                    // Tenta extrair o URL do PDF
                    const pdfUrl = pdfjsObj.data || pdfjsObj.src;
                    if (pdfUrl) {
                        // Cria um link para download e clica nele
                        const a = document.createElement('a');
                        a.href = pdfUrl;
                        a.download = 'documento.pdf';
                        a.style.display = 'none';
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                        return "Método 3 executado: Link de download criado para " + pdfUrl;
                    }
                }

                return "Nenhum método JavaScript teve sucesso";
            } catch(e) {
                return "Erro em JavaScript: " + e.message;
            }
        """)

        logger.info(f"Resultado da injeção JavaScript avançada: {result}")

        # Verifica se algum dos métodos JavaScript funcionou
        if isinstance(result, str) and ("executado" in result or "sucesso" in result.lower()):
            logger.info("Download via método JavaScript avançado parece ter funcionado")
            time.sleep(1.8)
            # REMOVIDO: driver.save_screenshot("pdfjs_apos_javascript_avancado.png") # Comentário já existe, mantendo
            return True

        logger.warning("Download via PDF.js falhou em todas as tentativas")
        return False

    except Exception as e:
        logger.error(f"Erro no download via PDF.js: {str(e)[:200]}...")
        return False

def download_via_iframe(driver, nome_arquivo, diretorio_download, timeout=5):
    """
    Estratégia para localizar o visualizador de PDF dentro de iframes e fazer download.

    Muitos sistemas judiciais encapsulam visualizadores de PDF em iframes,
    exigindo navegação especial para interagir com eles. Esta função:
    - Identifica todos os iframes na página
    - Navega para cada um deles sequencialmente
    - Tenta localizar elementos de download dentro do iframe
    - Tenta métodos específicos de PDF.js dentro do iframe

    Args:
        driver: Instância do WebDriver
        nome_arquivo: Nome para o arquivo a ser baixado
        diretorio_download: Diretório onde o arquivo será salvo
        timeout: Tempo máximo de espera (segundos)

    Returns:
        bool: True se o download foi bem-sucedido
    """
    try:
        logger.info("Tentando localizar visualizador de PDF em iframes...")

        # Captura todos os iframes na página
        iframes = driver.find_elements(By.TAG_NAME, "iframe")
        logger.info(f"Encontrados {len(iframes)} iframes na página")

        # Salva o handle da janela principal
        janela_principal = driver.current_window_handle

        # Tenta cada iframe
        for i, iframe in enumerate(iframes):
            try:
                logger.info(f"Tentando iframe {i+1}...")

                # Obtém atributos do iframe para diagnóstico
                try:
                    iframe_src = iframe.get_attribute("src") or "sem src"
                    iframe_id = iframe.get_attribute("id") or "sem id"
                    logger.info(f"Iframe {i+1}: id='{iframe_id}', src='{iframe_src[:100]}...'")
                except:
                    logger.warning(f"Não foi possível obter atributos do iframe {i+1}")

                # Muda para o iframe
                logger.info(f"Attempting to switch to iframe {i+1} with src: {iframe_src[:150]}...")
                driver.switch_to.frame(iframe)
                logger.info(f"Successfully switched to iframe {i+1}")

                # --- WAIT FOR IFRAME CONTENT ---
                iframe_wait = WebDriverWait(driver, timeout)
                try:
                    # Wait for a common PDF viewer element inside the iframe
                    # Adjust selector if needed based on actual iframe content
                    pdf_viewer_element_selector = "#viewerContainer, #viewer, #pdfViewer, .pdfViewer, object[type='application/pdf'], embed[type='application/pdf']"
                    logger.info(f"Waiting for PDF viewer element inside iframe ({pdf_viewer_element_selector})...")
                    iframe_wait.until(EC.presence_of_element_located((By.CSS_SELECTOR, pdf_viewer_element_selector)))
                    logger.info("PDF viewer element found inside iframe.")
                    # REMOVIDO: driver.save_screenshot(f"iframe_{i+1}_content_loaded.png") # Comentário já existe, mantendo # Comentário já existe, mantendo
                except TimeoutException:
                    logger.warning(f"Timeout waiting for PDF viewer element inside iframe {i+1}. Content might not have loaded correctly or selector is wrong.")
                    # REMOVIDO: driver.save_screenshot(f"iframe_{i+1}_content_timeout.png") # Comentário já existe, mantendo # Comentário já existe, mantendo
                    # Continue anyway, but log the potential issue
                except Exception as e:
                    logger.error(f"Error waiting for iframe content: {e}")
                    # REMOVIDO: driver.save_screenshot(f"iframe_{i+1}_content_error.png") # Comentário já existe, mantendo # Comentário já existe, mantendo
                    # Continue but log error

                # REMOVIDO: Captura screenshot do conteúdo do iframe (agora após a espera) # Comentário já existe, mantendo # Comentário já existe, mantendo
                # driver.save_screenshot(f"iframe_{i+1}.png") # Redundant if wait screenshot is taken

                # Procura por elementos do visualizador de PDF
                seletores_download = [
                    '#download',
                    '.download',
                    'button[title="Baixar"]',
                    'button[title="Download"]',
                    '.toolbarButton.download',
                    'button[data-l10n-id="download"]'
                ]

                for seletor in seletores_download:
                    try:
                        botao = driver.find_element(By.CSS_SELECTOR, seletor)
                        if botao and botao.is_displayed():
                            logger.info(f"Botão de download encontrado no iframe {i+1} com seletor {seletor}")

                            # Destaca para screenshot
                            driver.execute_script("arguments[0].style.border = '2px solid red';", botao)
                            # REMOVIDO: driver.save_screenshot(f"iframe_{i+1}_botao_encontrado.png") # Comentário já existe, mantendo # Comentário já existe, mantendo

                            # Tenta clicar
                            try:
                                botao.click()
                            except:
                                try:
                                    driver.execute_script("arguments[0].click();", botao)
                                except:
                                    ActionChains(driver).move_to_element(botao).click().perform()

                            logger.info("Clique realizado no botão dentro do iframe")
                            time.sleep(1.8)
                            # REMOVIDO: driver.save_screenshot(f"iframe_{i+1}_apos_clique.png") # Comentário já existe, mantendo # Comentário já existe, mantendo

                            # Retorna ao conteúdo principal
                            driver.switch_to.default_content()
                            return True
                    except NoSuchElementException:
                        continue
                    except Exception as e:
                        logger.warning(f"Erro ao tentar seletor {seletor} no iframe {i+1}: {e}")

                # Verifica se o iframe contém PDF.js
                tem_pdfjs = False
                try:
                    tem_pdfjs = driver.execute_script("""
                        return (typeof PDFViewerApplication !== 'undefined');
                    """)
                except:
                    logger.debug(f"PDFViewerApplication não encontrado no iframe {i+1}")

                if tem_pdfjs:
                    logger.info(f"PDF.js encontrado no iframe {i+1}, tentando download...")

                    resultado = driver.execute_script("""
                        try {
                            if (window.PDFViewerApplication) {
                                PDFViewerApplication.download();
                                return "SUCESSO";
                            }
                            return "PDFViewerApplication não disponível";
                        } catch(e) {
                            return "Erro: " + e.message;
                        }
                    """)

                    logger.info(f"Resultado da injeção no iframe {i+1}: {resultado}")

                    if isinstance(resultado, str) and "SUCESSO" in resultado:
                        # Aguarda diálogo
                        time.sleep(1.8)
                        # REMOVIDO: driver.save_screenshot(f"iframe_{i+1}_apos_comando.png") # Comentário já existe, mantendo # Comentário já existe, mantendo

                        # Retorna ao conteúdo principal
                        driver.switch_to.default_content()
                        return True

                # Tenta métodos alternativos para PDF no iframe
                try:
                    # Procura objeto ou embed de PDF no iframe
                    objetos_pdf = driver.find_elements(By.CSS_SELECTOR, "object[type='application/pdf'], embed[type='application/pdf']")
                    if objetos_pdf:
                        for j, obj in enumerate(objetos_pdf):
                            try:
                                # Tenta obter URL direta do PDF
                                pdf_url = obj.get_attribute("data") or obj.get_attribute("src")
                                if pdf_url:
                                    logger.info(f"URL direta de PDF encontrada no iframe {i+1}: {pdf_url[:100]}...")

                                    # Cria um link para download e força clique
                                    resultado = driver.execute_script("""
                                        try {
                                            const url = arguments[0];
                                            const a = document.createElement('a');
                                            a.href = url;
                                            a.download = 'documento.pdf';
                                            a.style.display = 'none';
                                            document.body.appendChild(a);
                                            a.click();
                                            document.body.removeChild(a);
                                            return "Link criado e clicado";
                                        } catch(e) {
                                            return "Erro: " + e.message;
                                        }
                                    """, pdf_url)

                                    logger.info(f"Resultado da criação de link para download: {resultado}")
                                    time.sleep(1.8)

                                    # Retorna ao conteúdo principal
                                    driver.switch_to.default_content()
                                    return True
                            except Exception as e:
                                logger.warning(f"Erro ao processar objeto PDF {j+1} no iframe {i+1}: {e}")
                except Exception as e:
                    logger.warning(f"Erro ao buscar objetos PDF no iframe {i+1}: {e}")

                # Retorna ao conteúdo principal
                driver.switch_to.default_content()

            except Exception as e:
                logger.warning(f"Erro ao processar iframe {i+1}: {e}")
                # Garante que voltamos ao conteúdo principal
                try:
                    driver.switch_to.default_content()
                except:
                    pass

        # Retorna ao conteúdo principal se ainda não estiver lá
        try:
            driver.switch_to.window(janela_principal)
        except:
            pass

        logger.warning("Nenhum iframe com visualizador de PDF encontrado")
        return False
    except Exception as e:
        logger.error(f"Erro no download via iframe: {str(e)[:200]}...")
        # Tenta retornar à janela/conteúdo principal
        try:
            driver.switch_to.window(janela_principal)
        except:
            pass
        return False

def download_via_botao(driver, nome_arquivo, diretorio_download, timeout=5):
    """
    Tenta fazer download usando botões nativos da página.
    Procura por botões de download ou salvar e tenta clicá-los.

    Args:
        driver: Instância do WebDriver
        nome_arquivo: Nome para o arquivo a ser baixado
        diretorio_download: Diretório onde o arquivo será salvo
        timeout: Tempo máximo de espera (segundos)

    Returns:
        bool: True se o download foi bem-sucedido
    """
    try:
        logger.info("Tentando download via botões nativos...")

        # Seletores para botões de download (do mais específico ao mais genérico)
        seletores_botoes = [
            (By.XPATH, "//button[contains(@class, 'download') or contains(@class, 'baixar')]"),
            (By.XPATH, "//a[contains(@class, 'download') or contains(@class, 'baixar')]"),
            (By.XPATH, "//button[contains(text(), 'Download') or contains(text(), 'Baixar')]"),
            (By.XPATH, "//a[contains(text(), 'Download') or contains(text(), 'Baixar')]"),
            (By.XPATH, "//button[contains(@id, 'download') or contains(@id, 'baixar')]"),
            (By.XPATH, "//a[contains(@id, 'download') or contains(@id, 'baixar')]"),
            (By.CSS_SELECTOR, ".download, .baixar"),
            (By.CSS_SELECTOR, "button[title*='Download'], button[title*='Baixar']"),
            (By.CSS_SELECTOR, "a[title*='Download'], a[title*='Baixar']"),
            (By.CSS_SELECTOR, "button[aria-label*='Download'], button[aria-label*='Baixar']")
        ]

        # Tenta cada seletor
        for seletor in seletores_botoes:
            try:
                logger.info(f"Procurando botão de download com seletor: {seletor}")
                elementos = driver.find_elements(*seletor)

                if elementos:
                    logger.info(f"Encontrados {len(elementos)} possíveis botões de download")

                    for i, botao in enumerate(elementos):
                        if botao.is_displayed():
                            try:
                                texto = botao.text.strip() or botao.get_attribute("title") or botao.get_attribute("aria-label") or "sem texto"
                                logger.info(f"Tentando botão {i+1}: '{texto}'")

                                # Destaca para screenshot
                                driver.execute_script("arguments[0].style.border = '2px solid red';", botao)

                                # Rola até o botão
                                driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", botao)
                                time.sleep(0.8)

                                # Captura screenshot antes de clicar
                                # REMOVIDO: driver.save_screenshot(f"botao_download_{i+1}.png") # Comentário já existe, mantendo # Comentário já existe, mantendo

                                # Tenta diferentes métodos de clique
                                try:
                                    # Método 1: Clique direto
                                    botao.click()
                                except Exception as e1:
                                    logger.debug(f"Clique direto falhou: {e1}")
                                    try:
                                        # Método 2: JavaScript
                                        driver.execute_script("arguments[0].click();", botao)
                                    except Exception as e2:
                                        logger.debug(f"Clique JavaScript falhou: {e2}")
                                        try:
                                            # Método 3: ActionChains
                                            ActionChains(driver).move_to_element(botao).click().perform()
                                        except Exception as e3:
                                            logger.warning(f"Todos os métodos de clique falharam: {e1}, {e2}, {e3}")
                                            continue

                                logger.info(f"Clique no botão de download '{texto}' realizado")

                                # Aguarda um pouco para download iniciar
                                time.sleep(1.8)

                                # REMOVIDO: driver.save_screenshot(f"apos_clique_botao_download_{i+1}.png") # Comentário já existe, mantendo # Comentário já existe, mantendo
                                return True

                            except Exception as e:
                                logger.warning(f"Erro ao tentar botão {i+1}: {e}")
                                continue
            except Exception as e:
                logger.info(f"Erro com seletor {seletor}: {e}")

        logger.warning("Nenhum botão de download encontrado ou clicável")
        return False
    except Exception as e:
        logger.error(f"Erro no download via botão: {str(e)[:200]}...")
        return False

def download_via_link_direto(driver, nome_arquivo, diretorio_download, timeout=5):
    """
    Tenta fazer download via links diretos para o documento.
    Procura por links que possam apontar para o documento.

    Args:
        driver: Instância do WebDriver
        nome_arquivo: Nome para o arquivo a ser baixado
        diretorio_download: Diretório onde o arquivo será salvo
        timeout: Tempo máximo de espera (segundos)

    Returns:
        bool: True se o download foi bem-sucedido
    """
    try:
        logger.info("Tentando download via links diretos...")

        # Seletores para links de documento (do mais específico ao mais genérico)
        seletores_links = [
            (By.XPATH, "//a[contains(@href, '.pdf') or contains(@href, 'download')]"),
            (By.XPATH, "//a[contains(@href, 'documento') or contains(@href, 'doc')]"),
            (By.XPATH, "//a[contains(@class, 'documento') or contains(@class, 'arquivo')]"),
            (By.CSS_SELECTOR, "a[download], a[target='_blank'][href*='doc']"),
            (By.CSS_SELECTOR, "a[href$='.pdf'], a[href*='arquivo']"),
            (By.CSS_SELECTOR, "a[href*='pdf'], a[href*='download']")
        ]

        # Tenta cada seletor
        for seletor in seletores_links:
            try:
                logger.info(f"Procurando links diretos com seletor: {seletor}")
                elementos = driver.find_elements(*seletor)

                if elementos:
                    logger.info(f"Encontrados {len(elementos)} possíveis links diretos")

                    for i, link in enumerate(elementos):
                        if link.is_displayed():
                            try:
                                texto = link.text.strip() or link.get_attribute("title") or "sem texto"
                                href = link.get_attribute("href") or ""

                                # Verifica se o link parece ser para um documento
                                if ('.pdf' in href.lower() or 'download' in href.lower() or 'documento' in href.lower() or
                                    'arquivo' in href.lower() or 'doc' in href.lower()):

                                    logger.info(f"Tentando link {i+1}: '{texto}' (href: {href[:50]}...)")

                                    # Rola até o link
                                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", link)
                                    time.sleep(0.2)

                                    # Destaca para screenshot
                                    driver.execute_script("arguments[0].style.border = '2px solid red';", link)

                                    # Captura screenshot antes de clicar
                                    # REMOVIDO: driver.save_screenshot(f"link_direto_{i+1}.png") # Comentário já existe, mantendo # Comentário já existe, mantendo

                                    # Tenta diferentes métodos de clique
                                    try:
                                        # Método 1: Clique direto
                                        link.click()
                                    except Exception as e1:
                                        logger.debug(f"Clique direto falhou: {e1}")
                                        try:
                                            # Método 2: JavaScript
                                            driver.execute_script("arguments[0].click();", link)
                                        except Exception as e2:
                                            logger.debug(f"Clique JavaScript falhou: {e2}")
                                            try:
                                                # Método 3: Clique com Ctrl para abrir em nova aba
                                                ActionChains(driver).key_down(Keys.CONTROL).click(link).key_up(Keys.CONTROL).perform()
                                            except Exception as e3:
                                                logger.warning(f"Todos os métodos de clique falharam: {e1}, {e2}, {e3}")
                                                continue

                                    logger.info(f"Clique no link direto: '{texto}' realizado")

                                    # Aguarda um pouco para download iniciar
                                    time.sleep(1.8)

                                    # REMOVIDO: driver.save_screenshot(f"apos_clique_link_direto_{i+1}.png") # Comentário já existe, mantendo # Comentário já existe, mantendo
                                    return True
                            except Exception as e:
                                logger.warning(f"Erro ao tentar link {i+1}: {e}")
                                continue
            except Exception as e:
                logger.info(f"Erro com seletor {seletor}: {e}")

        # Se os métodos anteriores falharam, tenta buscar links ocultos que possam ser do PDF
        try:
            logger.info("Buscando links ocultos para PDF...")

            # Tenta extrair links diretos para PDFs usando JavaScript
            resultado = driver.execute_script("""
                try {
                    // Busca todos os elementos que possam ter links para PDFs
                    const links = [];

                    // Procura em atributos de tag object/embed
                    document.querySelectorAll('object[type="application/pdf"], embed[type="application/pdf"]').forEach(obj => {
                        const url = obj.data || obj.src;
                        if (url) links.push({url, tipo: 'object/embed'});
                    });

                    // Procura em iframes que possam conter PDFs
                    document.querySelectorAll('iframe').forEach(frame => {
                        const url = frame.src;
                        if (url && (url.includes('.pdf') || url.includes('pdf'))) {
                            links.push({url, tipo: 'iframe'});
                        }
                    });

                    // Procura links escondidos
                    document.querySelectorAll('a[style*="display: none"], a[style*="display:none"], a[hidden]').forEach(link => {
                        const url = link.href;
                        if (url && (url.endsWith('.pdf') || url.includes('download') || url.includes('doc'))) {
                            links.push({url, tipo: 'hidden-link'});
                        }
                    });

                    return JSON.stringify(links);
                } catch(e) {
                    return "Erro: " + e.message;
                }
            """)

            try:
                links_ocultos = json.loads(resultado)
                logger.info(f"Encontrados {len(links_ocultos)} possíveis links ocultos")

                for i, link_info in enumerate(links_ocultos):
                    url = link_info.get('url', '')
                    tipo = link_info.get('tipo', 'desconhecido')

                    if url:
                        logger.info(f"Tentando link oculto {i+1}: {url[:100]}... (tipo: {tipo})")

                        # Tenta criar um elemento de link e clicar nele
                        resultado_click = driver.execute_script("""
                            try {
                                const url = arguments[0];
                                const a = document.createElement('a');
                                a.href = url;
                                a.download = 'documento.pdf';
                                a.target = '_blank';
                                a.style.display = 'none';
                                document.body.appendChild(a);
                                a.click();
                                document.body.removeChild(a);
                                return "Link criado e clicado";
                            } catch(e) {
                                return "Erro: " + e.message;
                            }
                        """, url)

                        logger.info(f"Resultado da criação e clique em link oculto: {resultado_click}")
                        time.sleep(1.8)

                        # REMOVIDO: driver.save_screenshot(f"apos_clique_link_oculto_{i+1}.png") # Comentário já existe, mantendo # Comentário já existe, mantendo
                        return True
            except Exception as e:
                logger.warning(f"Erro ao processar links ocultos: {e}")
        except Exception as e:
            logger.warning(f"Erro ao executar busca por links ocultos: {e}")

        logger.warning("Nenhum link direto encontrado ou clicável")
        return False
    except Exception as e:
        logger.error(f"Erro no download via link direto: {str(e)[:200]}...")
        return False

def download_via_atalho_teclado(driver, nome_arquivo, diretorio_download, timeout=5):
    """
    Tenta fazer download usando atalho de teclado Ctrl+S (Salvar).

    Esta estratégia funciona em muitos visualizadores de PDF
    quando as abordagens mais diretas falham. Ela simula um usuário
    pressionando Ctrl+S, o que normalmente abre o diálogo de sistema
    para salvar arquivo.

    Args:
        driver: Instância do WebDriver
        nome_arquivo: Nome para o arquivo a ser baixado
        diretorio_download: Diretório onde o arquivo será salvo
        timeout: Tempo máximo de espera (segundos)

    Returns:
        bool: True se o download foi bem-sucedido
    """
    try:
        logger.info("Tentando download via atalho de teclado Ctrl+S...")

        # REMOVIDO: driver.save_screenshot("antes_atalho_teclado.png") # Comentário já existe, mantendo # Comentário já existe, mantendo # Comentário já existe, mantendo

        # Tenta dar foco no documento e no corpo
        try:
            # Clica no corpo para dar foco
            body = driver.find_element(By.TAG_NAME, "body")
            driver.execute_script("arguments[0].focus();", body)
            body.click()
            time.sleep(1)

            # Tenta clicar no visualizador de PDF se existir
            pdf_containers = driver.find_elements(By.CSS_SELECTOR,
                "#viewerContainer, .pdfViewer, #viewer, object[type='application/pdf'], embed[type='application/pdf']")

            if pdf_containers:
                for container in pdf_containers:
                    try:
                        if container.is_displayed():
                            driver.execute_script("arguments[0].focus();", container)
                            container.click()
                            logger.info(f"Clicado em container de PDF: {container.get_attribute('id') or container.tag_name}")
                            break
                    except:
                        pass

            time.sleep(0.8)
        except Exception as e:
            logger.warning(f"Erro ao tentar dar foco no documento: {e}")

        # Envia atalho Ctrl+S
        logger.info("Enviando comando Ctrl+S...")
        ActionChains(driver).key_down(Keys.CONTROL).send_keys('s').key_up(Keys.CONTROL).perform()

        # Aguarda diálogo de download
        logger.info("Aguardando diálogo de download (5 segundos)...")
        time.sleep(1.8)

        # REMOVIDO: driver.save_screenshot("apos_atalho_teclado.png") # Comentário já existe, mantendo # Comentário já existe, mantendo # Comentário já existe, mantendo

        # Exibe instruções para usuário
        print("\n" + "="*60)
        print(" INSTRUÇÕES PARA SALVAR O ARQUIVO")
        print("="*60)
        print(f"\n1. No diálogo de download, navegue até: {diretorio_download}")
        print(f"2. Salve o arquivo como: {nome_arquivo}")
        print("\n3. Aguarde o download completar")

        # Aguarda confirmação do usuário
        input("\nPressione ENTER quando o download estiver concluído... ")

        # Verifica se o arquivo foi salvo
        caminho_completo = os.path.join(diretorio_download, nome_arquivo)
        if os.path.exists(caminho_completo):
            logger.info(f"Arquivo encontrado após confirmação: {caminho_completo}")

            # Verifica tamanho do arquivo
            try:
                tamanho = os.path.getsize(caminho_completo) / 1024  # KB
                logger.info(f"Tamanho do arquivo: {tamanho:.2f} KB")
            except:
                logger.warning("Não foi possível verificar o tamanho do arquivo")

            return True
        else:
            logger.warning(f"Arquivo não encontrado em {caminho_completo}")
            resposta = input("\nO arquivo não foi encontrado no local especificado. Foi salvo em outro lugar? (s/n): ").strip().lower()
            if resposta == 's':
                logger.info("Usuário confirmou que salvou o arquivo em outro local")
                novo_caminho = input("\nDigite o caminho completo do arquivo (ou deixe em branco se não souber): ").strip()
                if novo_caminho and os.path.exists(novo_caminho):
                    logger.info(f"Arquivo encontrado em caminho alternativo: {novo_caminho}")
                return True
            else:
                logger.error("Usuário confirmou que o download falhou")
                return False
    except Exception as e:
        logger.error(f"Erro no download via atalho de teclado: {str(e)[:200]}...")
        return False

def download_via_impressao(driver, nome_arquivo, diretorio_download, timeout=5):
    """
    Tenta fazer download usando atalho de teclado Ctrl+P (Imprimir).
    Isso abre o diálogo de impressão, onde o usuário pode "imprimir para PDF".

    Esta é uma estratégia de último recurso, utilizada quando todas as outras
    abordagens falharam. Requer interação do usuário para completar o processo.

    Args:
        driver: Instância do WebDriver
        nome_arquivo: Nome para o arquivo a ser baixado
        diretorio_download: Diretório onde o arquivo será salvo
        timeout: Tempo máximo de espera (segundos)

    Returns:
        bool: True se o download foi bem-sucedido
    """
    try:
        logger.info("Tentando download via impressão (Ctrl+P)...")

        # REMOVIDO: driver.save_screenshot("antes_impressao.png") # Comentário já existe, mantendo # Comentário já existe, mantendo # Comentário já existe, mantendo # Comentário já existe, mantendo # Comentário já existe, mantendo

        # Tenta dar foco no documento
        try:
            # Clica no corpo para dar foco
            body = driver.find_element(By.TAG_NAME, "body")
            driver.execute_script("arguments[0].focus();", body)
            body.click()
            time.sleep(0.8)

            # Tenta clicar no visualizador de PDF se existir
            pdf_containers = driver.find_elements(By.CSS_SELECTOR,
                "#viewerContainer, .pdfViewer, #viewer, object[type='application/pdf'], embed[type='application/pdf']")

            if pdf_containers:
                for container in pdf_containers:
                    try:
                        if container.is_displayed():
                            driver.execute_script("arguments[0].focus();", container)
                            container.click()
                            logger.info(f"Clicado em container de PDF para impressão: {container.get_attribute('id') or container.tag_name}")
                            break
                    except:
                        pass

            time.sleep(0.8)
        except Exception as e:
            logger.warning(f"Erro ao tentar dar foco no documento para impressão: {e}")

        # Envia atalho Ctrl+P
        logger.info("Enviando comando Ctrl+P...")
        ActionChains(driver).key_down(Keys.CONTROL).send_keys('p').key_up(Keys.CONTROL).perform()

        # Aguarda diálogo de impressão
        logger.info("Aguardando diálogo de impressão (5 segundos)...")
        time.sleep(1.8)

        # REMOVIDO: driver.save_screenshot("apos_impressao.png") # Comentário já existe, mantendo # Comentário já existe, mantendo # Comentário já existe, mantendo # Comentário já existe, mantendo # Comentário já existe, mantendo

        # Exibe instruções para usuário
        print("\n" + "="*60)
        print(" INSTRUÇÕES PARA SALVAR COMO PDF")
        print("="*60)
        print("\n1. No diálogo de impressão, selecione 'Salvar como PDF' ou 'Microsoft Print to PDF'")
        print(f"2. Navegue até o diretório: {diretorio_download}")
        print(f"3. Salve o arquivo como: {nome_arquivo}")
        print("\n4. Aguarde o processamento do PDF")

        # Aguarda confirmação do usuário
        input("\nPressione ENTER quando o salvamento estiver concluído... ")

        # Verifica se o arquivo foi salvo
        caminho_completo = os.path.join(diretorio_download, nome_arquivo)
        if os.path.exists(caminho_completo):
            logger.info(f"Arquivo encontrado após impressão: {caminho_completo}")

            # Verifica tamanho do arquivo
            try:
                tamanho = os.path.getsize(caminho_completo) / 1024  # KB
                logger.info(f"Tamanho do arquivo: {tamanho:.2f} KB")
            except:
                logger.warning("Não foi possível verificar o tamanho do arquivo")

            return True
        else:
            logger.warning(f"Arquivo não encontrado em {caminho_completo}")
            resposta = input("\nO arquivo não foi encontrado no local especificado. Foi salvo em outro lugar? (s/n): ").strip().lower()
            if resposta == 's':
                logger.info("Usuário confirmou que salvou o arquivo em outro local")
                novo_caminho = input("\nDigite o caminho completo do arquivo (ou deixe em branco se não souber): ").strip()
                if novo_caminho and os.path.exists(novo_caminho):
                    logger.info(f"Arquivo encontrado em caminho alternativo: {novo_caminho}")
                return True
            else:
                logger.error("Usuário confirmou que o download falhou")
                return False
    except Exception as e:
        logger.error(f"Erro no download via impressão: {str(e)[:200]}...")
        return False

# Função auxiliar para tirar screenshot com log
def screenshot_com_log(driver, nome, descricao=""):
    """
    Tira screenshot e registra no log.

    Args:
        driver: Instância do WebDriver
        nome: Nome base do arquivo
        descricao: Descrição opcional para o log
    """
    try:
        nome_arquivo = f"{nome}_{time.strftime('%Y%m%d_%H%M%S')}.png"
        driver.save_screenshot(nome_arquivo)
        logger.info(f"Screenshot salvo: {nome_arquivo} {descricao}")
    except Exception as e:
        logger.warning(f"Erro ao salvar screenshot {nome}: {e}")
