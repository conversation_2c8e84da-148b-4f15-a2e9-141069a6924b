#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TJSP Playwright Analysis - Análise Real do Site TJSP
Análise completa do site TJSP usando Playwright para mapear elementos, fluxos e backend
"""

import asyncio
import json
import os
import sys
from datetime import datetime
from playwright.async_api import async_playwright

# Configurações
TJSP_URL_CONSULTA = 'https://esaj.tjsp.jus.br/cpopg/open.do'
TJSP_URL_LOGIN = 'https://esaj.tjsp.jus.br/sajcas/login?service=https%3A%2F%2Fesaj.tjsp.jus.br%2Fesaj%2Fj_spring_cas_security_check'
ANALYSIS_OUTPUT_DIR = os.path.join(os.path.dirname(__file__), "..", "analysis")

class TJSPPlaywrightAnalyzer:
    def __init__(self):
        self.browser = None
        self.page = None
        self.analysis_data = {
            "timestamp": datetime.now().isoformat(),
            "urls_analyzed": [],
            "elements_mapped": {},
            "forms_detected": {},
            "scripts_found": [],
            "network_requests": [],
            "cookies": [],
            "local_storage": {},
            "session_storage": {},
            "page_structure": {},
            "login_flow": {},
            "search_flow": {},
            "download_flow": {}
        }
        
    async def initialize_browser(self):
        """Inicializa o navegador Playwright"""
        print("🚀 Inicializando Playwright...")
        self.playwright = await async_playwright().start()
        
        # Configurações do navegador
        self.browser = await self.playwright.chromium.launch(
            headless=False,  # Visível para análise
            args=[
                '--start-maximized',
                '--disable-blink-features=AutomationControlled',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor'
            ]
        )
        
        # Criar contexto com configurações específicas
        context = await self.browser.new_context(
            viewport={'width': 1920, 'height': 1080},
            user_agent='Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        )
        
        self.page = await context.new_page()
        
        # Interceptar requisições de rede
        await self.page.route("**/*", self.intercept_network_requests)
        
        print("✅ Playwright inicializado com sucesso!")
        
    async def intercept_network_requests(self, route):
        """Intercepta e registra requisições de rede"""
        request = route.request
        self.analysis_data["network_requests"].append({
            "url": request.url,
            "method": request.method,
            "headers": dict(request.headers),
            "timestamp": datetime.now().isoformat()
        })
        await route.continue_()
        
    async def analyze_page_structure(self, page_name):
        """Analisa a estrutura completa da página"""
        print(f"🔍 Analisando estrutura da página: {page_name}")
        
        # Mapear todos os elementos importantes
        elements = {}
        
        # Formulários
        forms = await self.page.query_selector_all('form')
        elements['forms'] = []
        for i, form in enumerate(forms):
            form_data = {
                'index': i,
                'action': await form.get_attribute('action'),
                'method': await form.get_attribute('method'),
                'id': await form.get_attribute('id'),
                'class': await form.get_attribute('class'),
                'inputs': []
            }
            
            # Inputs dentro do formulário
            inputs = await form.query_selector_all('input, select, textarea')
            for input_elem in inputs:
                input_data = {
                    'type': await input_elem.get_attribute('type'),
                    'name': await input_elem.get_attribute('name'),
                    'id': await input_elem.get_attribute('id'),
                    'class': await input_elem.get_attribute('class'),
                    'placeholder': await input_elem.get_attribute('placeholder'),
                    'value': await input_elem.get_attribute('value')
                }
                form_data['inputs'].append(input_data)
            
            elements['forms'].append(form_data)
        
        # Botões
        buttons = await self.page.query_selector_all('button, input[type="submit"], input[type="button"]')
        elements['buttons'] = []
        for button in buttons:
            button_data = {
                'text': await button.inner_text(),
                'type': await button.get_attribute('type'),
                'id': await button.get_attribute('id'),
                'class': await button.get_attribute('class'),
                'onclick': await button.get_attribute('onclick')
            }
            elements['buttons'].append(button_data)
        
        # Links importantes
        links = await self.page.query_selector_all('a[href]')
        elements['links'] = []
        for link in links:
            href = await link.get_attribute('href')
            if href and ('login' in href.lower() or 'consulta' in href.lower() or 'download' in href.lower()):
                link_data = {
                    'text': await link.inner_text(),
                    'href': href,
                    'id': await link.get_attribute('id'),
                    'class': await link.get_attribute('class')
                }
                elements['links'].append(link_data)
        
        # Scripts
        scripts = await self.page.query_selector_all('script[src]')
        elements['scripts'] = []
        for script in scripts:
            src = await script.get_attribute('src')
            if src:
                elements['scripts'].append(src)
        
        self.analysis_data["elements_mapped"][page_name] = elements
        
    async def analyze_cookies_and_storage(self):
        """Analisa cookies e storage"""
        print("🍪 Analisando cookies e storage...")
        
        # Cookies
        cookies = await self.page.context.cookies()
        self.analysis_data["cookies"] = [
            {
                'name': cookie['name'],
                'value': cookie['value'],
                'domain': cookie['domain'],
                'path': cookie['path'],
                'secure': cookie.get('secure', False),
                'httpOnly': cookie.get('httpOnly', False)
            }
            for cookie in cookies
        ]
        
        # Local Storage
        try:
            local_storage = await self.page.evaluate('() => Object.fromEntries(Object.entries(localStorage))')
            self.analysis_data["local_storage"] = local_storage
        except:
            self.analysis_data["local_storage"] = {}
        
        # Session Storage
        try:
            session_storage = await self.page.evaluate('() => Object.fromEntries(Object.entries(sessionStorage))')
            self.analysis_data["session_storage"] = session_storage
        except:
            self.analysis_data["session_storage"] = {}
    
    async def analyze_tjsp_main_page(self):
        """Analisa a página principal do TJSP"""
        print("🏛️ Analisando página principal TJSP...")
        
        await self.page.goto(TJSP_URL_CONSULTA, wait_until='networkidle')
        self.analysis_data["urls_analyzed"].append(TJSP_URL_CONSULTA)
        
        # Aguardar carregamento completo
        await self.page.wait_for_timeout(3000)
        
        # Analisar estrutura
        await self.analyze_page_structure("main_page")
        await self.analyze_cookies_and_storage()
        
        # Capturar screenshot
        await self.page.screenshot(path=os.path.join(ANALYSIS_OUTPUT_DIR, "tjsp_main_page.png"))
        
        # Verificar se há captcha
        captcha_elements = await self.page.query_selector_all('img[src*="captcha"], img[src*="Captcha"], #captcha, .captcha')
        if captcha_elements:
            print("🔒 CAPTCHA detectado na página principal")
            self.analysis_data["captcha_detected"] = True
        
    async def analyze_login_flow(self):
        """Analisa o fluxo de login"""
        print("🔐 Analisando fluxo de login...")
        
        await self.page.goto(TJSP_URL_LOGIN, wait_until='networkidle')
        self.analysis_data["urls_analyzed"].append(TJSP_URL_LOGIN)
        
        await self.page.wait_for_timeout(2000)
        
        # Analisar estrutura da página de login
        await self.analyze_page_structure("login_page")
        
        # Capturar screenshot
        await self.page.screenshot(path=os.path.join(ANALYSIS_OUTPUT_DIR, "tjsp_login_page.png"))
        
    async def test_search_functionality(self):
        """Testa funcionalidade de pesquisa"""
        print("🔍 Testando funcionalidade de pesquisa...")
        
        # Voltar para página principal
        await self.page.goto(TJSP_URL_CONSULTA, wait_until='networkidle')
        await self.page.wait_for_timeout(2000)
        
        # Tentar localizar campo de pesquisa por número do processo
        search_selectors = [
            'input[name*="numero"]',
            'input[name*="processo"]',
            'input[id*="numero"]',
            'input[id*="processo"]',
            '#numeroDigitoAnoUnificado',
            '#fPP\\:numeroDigitoAnoUnificado\\:numeroDigitoAnoUnificado'
        ]
        
        search_field = None
        for selector in search_selectors:
            try:
                search_field = await self.page.query_selector(selector)
                if search_field:
                    print(f"✅ Campo de pesquisa encontrado: {selector}")
                    break
            except:
                continue
        
        if search_field:
            # Testar preenchimento com número fictício
            await search_field.fill("1234567-89.2023.8.26.0001")
            await self.page.wait_for_timeout(1000)
            
            # Capturar screenshot com campo preenchido
            await self.page.screenshot(path=os.path.join(ANALYSIS_OUTPUT_DIR, "tjsp_search_filled.png"))
            
            self.analysis_data["search_flow"]["field_found"] = True
            self.analysis_data["search_flow"]["field_selector"] = selector
        else:
            print("❌ Campo de pesquisa não encontrado")
            self.analysis_data["search_flow"]["field_found"] = False
    
    async def run_complete_analysis(self):
        """Executa análise completa do TJSP"""
        try:
            # Criar diretório de análise
            os.makedirs(ANALYSIS_OUTPUT_DIR, exist_ok=True)
            
            await self.initialize_browser()
            
            # Análises sequenciais
            await self.analyze_tjsp_main_page()
            await self.analyze_login_flow()
            await self.test_search_functionality()
            
            # Salvar dados da análise
            analysis_file = os.path.join(ANALYSIS_OUTPUT_DIR, "tjsp_complete_analysis.json")
            with open(analysis_file, 'w', encoding='utf-8') as f:
                json.dump(self.analysis_data, f, indent=2, ensure_ascii=False)
            
            print(f"✅ Análise completa salva em: {analysis_file}")
            
        except Exception as e:
            print(f"❌ Erro durante análise: {e}")
            import traceback
            traceback.print_exc()
        
        finally:
            if self.browser:
                await self.browser.close()
            if hasattr(self, 'playwright'):
                await self.playwright.stop()

async def main():
    """Função principal"""
    print("🎯 TJSP Playwright Analysis - Iniciando...")
    
    analyzer = TJSPPlaywrightAnalyzer()
    await analyzer.run_complete_analysis()
    
    print("🏁 Análise concluída!")

if __name__ == "__main__":
    asyncio.run(main())
