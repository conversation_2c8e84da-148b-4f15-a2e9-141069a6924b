#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Universal System Manager - TJSP Integrado
Sistema universal para detecção automática de usuário, gerenciamento de ChromeDriver e configurações
"""

import os
import sys
import platform
import getpass
import json
import logging
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple

# Importações para webdriver_manager
try:
    from webdriver_manager.chrome import ChromeDriverManager
    from webdriver_manager.core.os_manager import ChromeType
    WEBDRIVER_MANAGER_AVAILABLE = True
except ImportError:
    WEBDRIVER_MANAGER_AVAILABLE = False
    print("⚠️ webdriver_manager não instalado. Execute: pip install webdriver-manager")

class UniversalSystemManager:
    """
    Gerenciador universal do sistema TJSP
    Detecta automaticamente usuário, sistema operacional e configura ChromeDriver
    """
    
    def __init__(self):
        self.current_user = getpass.getuser()
        self.system_info = self._detect_system_info()
        self.project_root = Path(__file__).parent.parent
        self.config_dir = self.project_root / "config"
        self.logs_dir = self.project_root / "logs"
        self.downloads_dir = self.project_root / "downloads"
        self.drivers_dir = self.project_root / "drivers"
        
        # Criar diretórios necessários
        self._create_directories()
        
        # Configurar logging
        self.logger = self._setup_logging()
        
        # Configurações do usuário
        self.user_config = self._load_or_create_user_config()
        
        self.logger.info(f"Sistema inicializado para usuário: {self.current_user}")
        self.logger.info(f"Sistema operacional: {self.system_info['os']} {self.system_info['version']}")
        
    def _detect_system_info(self) -> Dict:
        """Detecta informações do sistema operacional"""
        return {
            'os': platform.system(),
            'version': platform.version(),
            'architecture': platform.architecture()[0],
            'machine': platform.machine(),
            'processor': platform.processor(),
            'python_version': platform.python_version(),
            'user': self.current_user,
            'home_dir': str(Path.home()),
            'temp_dir': os.environ.get('TEMP', '/tmp')
        }
    
    def _create_directories(self):
        """Cria diretórios necessários do projeto"""
        directories = [
            self.config_dir,
            self.logs_dir,
            self.downloads_dir,
            self.drivers_dir,
            self.downloads_dir / self.current_user,  # Diretório específico do usuário
            self.logs_dir / self.current_user        # Logs específicos do usuário
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def _setup_logging(self) -> logging.Logger:
        """Configura sistema de logging avançado"""
        logger = logging.getLogger(f'TJSP_Universal_{self.current_user}')
        logger.setLevel(logging.DEBUG)
        
        # Remover handlers existentes
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)
        
        # Formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # Handler para arquivo (específico do usuário)
        log_file = self.logs_dir / self.current_user / f"tjsp_universal_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        
        # Handler para console
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        
        return logger
    
    def _load_or_create_user_config(self) -> Dict:
        """Carrega ou cria configuração específica do usuário"""
        config_file = self.config_dir / f"user_config_{self.current_user}.json"
        
        default_config = {
            'user_info': {
                'username': self.current_user,
                'created_at': datetime.now().isoformat(),
                'last_updated': datetime.now().isoformat()
            },
            'chrome_profile': {
                'path': str(Path.home() / f"ClineAutomationProfile_TJSP_{self.current_user}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
                'auto_create': True
            },
            'download_settings': {
                'base_dir': str(self.downloads_dir / self.current_user),
                'timeout': 30,
                'max_retries': 3
            },
            'webdriver_settings': {
                'auto_update': True,
                'headless': False,
                'window_size': '1920,1080',
                'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            },
            'tjsp_settings': {
                'main_url': 'https://esaj.tjsp.jus.br/cpopg/open.do',
                'login_url': 'https://esaj.tjsp.jus.br/sajcas/login?service=https%3A%2F%2Fesaj.tjsp.jus.br%2Fesaj%2Fj_spring_cas_security_check',
                'search_field_selector': 'input[name*="numero"]',
                'timeout': 30
            }
        }
        
        if config_file.exists():
            try:
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                # Atualizar timestamp
                config['user_info']['last_updated'] = datetime.now().isoformat()
                self._save_user_config(config)
                return config
            except Exception as e:
                self.logger.warning(f"Erro ao carregar configuração existente: {e}")
                return default_config
        else:
            self._save_user_config(default_config)
            return default_config
    
    def _save_user_config(self, config: Dict):
        """Salva configuração do usuário"""
        config_file = self.config_dir / f"user_config_{self.current_user}.json"
        try:
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            self.logger.error(f"Erro ao salvar configuração: {e}")
    
    def get_chrome_driver_path(self) -> str:
        """
        Obtém caminho do ChromeDriver usando webdriver_manager
        Sistema automático que sempre funciona independente da versão do Chrome
        """
        if not WEBDRIVER_MANAGER_AVAILABLE:
            raise ImportError("webdriver_manager não está disponível. Execute: pip install webdriver-manager")
        
        try:
            self.logger.info("🔍 Localizando ChromeDriver automaticamente...")
            
            # Usar webdriver_manager para download/gerenciamento automático
            chrome_driver_path = ChromeDriverManager().install()
            
            self.logger.info(f"✅ ChromeDriver localizado: {chrome_driver_path}")
            
            # Salvar caminho na configuração para referência
            self.user_config['webdriver_settings']['last_chromedriver_path'] = chrome_driver_path
            self.user_config['webdriver_settings']['last_update'] = datetime.now().isoformat()
            self._save_user_config(self.user_config)
            
            return chrome_driver_path
            
        except Exception as e:
            self.logger.error(f"❌ Erro ao obter ChromeDriver: {e}")
            raise
    
    def get_chrome_profile_path(self) -> str:
        """Obtém caminho do perfil Chrome específico do usuário"""
        profile_path = self.user_config['chrome_profile']['path']
        
        # Criar diretório do perfil se não existir
        if self.user_config['chrome_profile']['auto_create']:
            Path(profile_path).mkdir(parents=True, exist_ok=True)
            self.logger.info(f"📁 Perfil Chrome criado/verificado: {profile_path}")
        
        return profile_path
    
    def get_download_directory(self) -> str:
        """Obtém diretório de download específico do usuário"""
        download_dir = self.user_config['download_settings']['base_dir']
        Path(download_dir).mkdir(parents=True, exist_ok=True)
        return download_dir
    
    def get_system_summary(self) -> Dict:
        """Retorna resumo completo do sistema"""
        return {
            'timestamp': datetime.now().isoformat(),
            'user': self.current_user,
            'system_info': self.system_info,
            'directories': {
                'project_root': str(self.project_root),
                'config': str(self.config_dir),
                'logs': str(self.logs_dir),
                'downloads': str(self.downloads_dir),
                'drivers': str(self.drivers_dir)
            },
            'chrome_profile': self.get_chrome_profile_path(),
            'download_directory': self.get_download_directory(),
            'webdriver_manager_available': WEBDRIVER_MANAGER_AVAILABLE,
            'config': self.user_config
        }
    
    def validate_system(self) -> Tuple[bool, List[str]]:
        """Valida se o sistema está configurado corretamente"""
        issues = []
        
        # Verificar webdriver_manager
        if not WEBDRIVER_MANAGER_AVAILABLE:
            issues.append("webdriver_manager não instalado")
        
        # Verificar diretórios
        required_dirs = [self.config_dir, self.logs_dir, self.downloads_dir]
        for directory in required_dirs:
            if not directory.exists():
                issues.append(f"Diretório não existe: {directory}")
        
        # Verificar permissões de escrita
        try:
            test_file = self.logs_dir / "test_write.tmp"
            test_file.write_text("test")
            test_file.unlink()
        except Exception as e:
            issues.append(f"Sem permissão de escrita em logs: {e}")
        
        # Verificar ChromeDriver (se webdriver_manager disponível)
        if WEBDRIVER_MANAGER_AVAILABLE:
            try:
                self.get_chrome_driver_path()
            except Exception as e:
                issues.append(f"Erro ao obter ChromeDriver: {e}")
        
        is_valid = len(issues) == 0
        return is_valid, issues
    
    def install_dependencies(self):
        """Instala dependências necessárias"""
        dependencies = [
            'webdriver-manager',
            'selenium',
            'requests',
            'beautifulsoup4'
        ]
        
        for dep in dependencies:
            try:
                import subprocess
                result = subprocess.run([sys.executable, '-m', 'pip', 'install', dep], 
                                      capture_output=True, text=True)
                if result.returncode == 0:
                    self.logger.info(f"✅ {dep} instalado com sucesso")
                else:
                    self.logger.error(f"❌ Erro ao instalar {dep}: {result.stderr}")
            except Exception as e:
                self.logger.error(f"❌ Erro ao instalar {dep}: {e}")

def main():
    """Função principal para teste do sistema"""
    print("🚀 TJSP Universal System Manager - Teste")
    
    try:
        # Inicializar sistema
        system = UniversalSystemManager()
        
        # Validar sistema
        is_valid, issues = system.validate_system()
        
        if not is_valid:
            print("❌ Problemas encontrados:")
            for issue in issues:
                print(f"  - {issue}")
            
            # Tentar instalar dependências
            print("🔧 Tentando instalar dependências...")
            system.install_dependencies()
        else:
            print("✅ Sistema validado com sucesso!")
        
        # Mostrar resumo
        summary = system.get_system_summary()
        print(f"\n📊 Resumo do Sistema:")
        print(f"  Usuário: {summary['user']}")
        print(f"  SO: {summary['system_info']['os']} {summary['system_info']['architecture']}")
        print(f"  Perfil Chrome: {summary['chrome_profile']}")
        print(f"  Downloads: {summary['download_directory']}")
        print(f"  WebDriver Manager: {'✅' if summary['webdriver_manager_available'] else '❌'}")
        
        # Testar ChromeDriver
        if WEBDRIVER_MANAGER_AVAILABLE:
            try:
                driver_path = system.get_chrome_driver_path()
                print(f"  ChromeDriver: ✅ {driver_path}")
            except Exception as e:
                print(f"  ChromeDriver: ❌ {e}")
        
    except Exception as e:
        print(f"❌ Erro crítico: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
