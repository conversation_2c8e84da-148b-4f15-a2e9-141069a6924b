#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
TJSP Simple Test - Teste básico do sistema universal
"""

import time
import tempfile
import os
import uuid
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

def test_tjsp_basic():
    """Teste básico do TJSP com webdriver_manager"""
    
    print("🎯 TJSP Simple Test - Teste Básico")
    
    try:
        # Criar diretório temporário único com timestamp e UUID
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
        unique_id = str(uuid.uuid4())[:8]
        temp_dir = os.path.join(tempfile.gettempdir(), f"tjsp_chrome_{timestamp}_{unique_id}")
        os.makedirs(temp_dir, exist_ok=True)
        print(f"📁 Diretório temporário único criado: {temp_dir}")

        # Configurar Chrome options básicas
        chrome_options = Options()

        # Usar diretório temporário único
        chrome_options.add_argument(f"--user-data-dir={temp_dir}")

        # Modo headless para evitar conflitos
        chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")

        chrome_options.add_argument("--disable-blink-features=AutomationControlled")
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        chrome_options.add_experimental_option("useAutomationExtension", False)

        chrome_options.add_argument("--no-first-run")
        chrome_options.add_argument("--no-default-browser-check")
        chrome_options.add_argument("--disable-extensions")
        chrome_options.add_argument("--disable-plugins")
        chrome_options.add_argument("--disable-images")
        chrome_options.add_argument("--disable-web-security")
        chrome_options.add_argument("--allow-running-insecure-content")
        chrome_options.add_argument("--disable-features=VizDisplayCompositor")
        
        print("⚙️ Opções do Chrome configuradas")
        
        # Obter ChromeDriver automaticamente
        print("🔍 Obtendo ChromeDriver automaticamente...")
        chromedriver_path = ChromeDriverManager().install()
        print(f"✅ ChromeDriver localizado: {chromedriver_path}")
        
        # Criar serviço
        service = Service(executable_path=chromedriver_path)
        
        # Inicializar driver
        print("🚀 Inicializando WebDriver...")
        driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # Configurações anti-detecção
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        print("✅ WebDriver inicializado com sucesso")
        
        # Navegar para TJSP
        url_tjsp = "https://esaj.tjsp.jus.br/cpopg/open.do"
        print(f"🌐 Navegando para: {url_tjsp}")
        
        driver.get(url_tjsp)
        
        # Aguardar carregamento
        WebDriverWait(driver, 30).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        print("✅ Página TJSP carregada")
        
        # Tentar localizar campo de pesquisa
        search_selectors = [
            'input[name*="numero"]',
            '#numeroDigitoAnoUnificado',
            'input[name="numeroDigitoAnoUnificado"]',
            'input[id*="numero"]'
        ]
        
        search_field = None
        for selector in search_selectors:
            try:
                search_field = driver.find_element(By.CSS_SELECTOR, selector)
                print(f"✅ Campo de pesquisa encontrado: {selector}")
                break
            except:
                continue
        
        if search_field:
            print("✅ Sistema funcionando corretamente!")
            
            # Testar preenchimento
            test_number = "1234567-89.2023.8.26.0001"
            search_field.clear()
            search_field.send_keys(test_number)
            print(f"✅ Campo preenchido com: {test_number}")
            
            # Aguardar um pouco para visualização
            time.sleep(3)
            
        else:
            print("⚠️ Campo de pesquisa não encontrado")
            
            # Mostrar elementos disponíveis para debug
            inputs = driver.find_elements(By.TAG_NAME, "input")
            print(f"📊 {len(inputs)} campos input encontrados:")
            for i, inp in enumerate(inputs[:10]):  # Mostrar apenas os primeiros 10
                try:
                    name = inp.get_attribute("name") or "sem nome"
                    id_attr = inp.get_attribute("id") or "sem id"
                    type_attr = inp.get_attribute("type") or "sem type"
                    print(f"  {i+1}. name='{name}', id='{id_attr}', type='{type_attr}'")
                except:
                    print(f"  {i+1}. Erro ao obter atributos")
        
        # Aguardar antes de fechar
        print("⏳ Aguardando 5 segundos antes de fechar...")
        time.sleep(5)
        
        # Fechar driver
        driver.quit()
        print("🧹 WebDriver finalizado")

        # Limpar diretório temporário
        try:
            import shutil
            shutil.rmtree(temp_dir)
            print(f"🗑️ Diretório temporário removido: {temp_dir}")
        except Exception as e:
            print(f"⚠️ Erro ao remover diretório temporário: {e}")

        return True
        
    except Exception as e:
        print(f"❌ Erro no teste: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_tjsp_basic()
    if success:
        print("\n🎉 TESTE CONCLUÍDO COM SUCESSO!")
        print("✅ WebDriver Manager funcionando")
        print("✅ ChromeDriver automático funcionando")
        print("✅ Navegação TJSP funcionando")
    else:
        print("\n❌ TESTE FALHOU!")
